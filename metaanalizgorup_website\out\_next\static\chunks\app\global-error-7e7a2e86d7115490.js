(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[219],{3188:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(5155);function n(e){let{error:s,reset:t}=e;return(0,r.jsx)("html",{children:(0,r.jsx)("body",{children:(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white shadow-lg rounded-lg p-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsxs)("div",{className:"mt-3 text-center",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Sistem Hatası"}),(0,r.jsx)("div",{className:"mt-2 px-7 py-3",children:(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Uygulama d\xfczeyinde bir hata oluştu. Sayfayı yenileyin."})}),(0,r.jsx)("div",{className:"items-center px-4 py-3",children:(0,r.jsx)("button",{onClick:t,className:"px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500",children:"Tekrar Dene"})})]})]})})})})}},4880:(e,s,t)=>{Promise.resolve().then(t.bind(t,3188))}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(4880)),_N_E=e.O()}]);