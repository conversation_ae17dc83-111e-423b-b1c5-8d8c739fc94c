(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[338],{750:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>y});var t=r(5155),s=r(2115),i=r(2596),l=r(9688);function n(){for(var e=arguments.length,a=Array(e),r=0;r<e;r++)a[r]=arguments[r];return(0,l.QP)((0,i.$)(a))}let d=s.forwardRef((e,a)=>{let{className:r,variant:s="default",hover:i=!0,children:l,...d}=e,c=n("rounded-lg transition-all duration-300",{default:"bg-white border border-gray-200 shadow-sm",modern:"bg-white rounded-xl shadow-lg border border-gray-100",glass:"glass backdrop-blur-lg"}[s],i?"hover:shadow-xl hover:-translate-y-1":"",r);return(0,t.jsx)("div",{className:c,ref:a,...d,children:l})});d.displayName="Card";let c=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:n("p-6 pb-0",r),...s})});c.displayName="CardHeader";let o=s.forwardRef((e,a)=>{let{className:r,children:s,...i}=e;return(0,t.jsx)("h3",{ref:a,className:n("text-xl font-semibold text-primary",r),...i,children:s})});o.displayName="CardTitle",s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("p",{ref:a,className:n("text-text-light",r),...s})}).displayName="CardDescription";let m=s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:n("p-6",r),...s})});m.displayName="CardContent",s.forwardRef((e,a)=>{let{className:r,...s}=e;return(0,t.jsx)("div",{ref:a,className:n("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter";let x=s.forwardRef((e,a)=>{let{className:r,variant:s="primary",size:i="md",children:l,...d}=e,c=n("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-pointer",{primary:"bg-primary hover:bg-primary-hover text-white shadow hover:shadow-lg",secondary:"bg-accent hover:bg-accent-hover text-primary shadow hover:shadow-lg",ghost:"hover:bg-gray-100 text-primary hover:text-primary-hover",glass:"glass-btn text-white hover:scale-105",outline:"border border-gray-300 bg-transparent hover:bg-gray-50 text-gray-700",default:"bg-blue-600 hover:bg-blue-700 text-white shadow hover:shadow-lg"}[s],{sm:"h-9 px-3 text-sm",md:"h-10 px-4 py-2",lg:"h-12 px-8 py-3 text-lg"}[i],r);return(0,t.jsx)("button",{className:c,ref:a,...d,children:l})});function g(e){let{className:a,variant:r="default",...s}=e;return(0,t.jsx)("div",{className:n("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}[r],a),...s})}x.displayName="Button";var u=r(2657),h=r(2525),p=r(9420),f=r(4186),b=r(1497);function y(){let[e,a]=(0,s.useState)([]),[r,i]=(0,s.useState)(!0),[l,n]=(0,s.useState)(null),[y,v]=(0,s.useState)(1),[j,N]=(0,s.useState)(1),[w,k]=(0,s.useState)("all"),z=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;try{i(!0);let r=await fetch(`/api/admin/callbacks.php?page=${e}&limit=10`,{credentials:"include"});if(!r.ok)throw Error("Failed to fetch callbacks");let t=await r.json();t.success?(a(t.data.callbacks),v(t.data.pagination.page),N(t.data.pagination.totalPages)):n(t.message)}catch(e){n(e instanceof Error?e.message:"An error occurred")}finally{i(!1)}},C=async e=>{try{let r=await fetch("/api/admin/callbacks.php",{method:"PATCH",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({id:e,action:"mark_read"})}),t=await r.json();t.success?a(a=>a.map(a=>a.id===e?{...a,is_read:!0}:a)):n(t.message)}catch(e){n(e instanceof Error?e.message:"Failed to mark as read")}},A=async e=>{if(confirm("Bu geri arama talebini silmek istediğinizden emin misiniz?"))try{let r=await fetch(`/api/admin/callbacks.php?id=${e}`,{method:"DELETE",credentials:"include"}),t=await r.json();t.success?a(a=>a.filter(a=>a.id!==e)):n(t.message)}catch(e){n(e instanceof Error?e.message:"Failed to delete callback")}},_=e=>new Date(e).toLocaleString("tr-TR"),E=e=>{switch(e){case"pending":return(0,t.jsx)(g,{className:"bg-yellow-100 text-yellow-800",children:"Bekliyor"});case"called":return(0,t.jsx)(g,{className:"bg-green-100 text-green-800",children:"Arandı"});default:return(0,t.jsx)(g,{className:"bg-gray-100 text-gray-800",children:e})}},S=e=>{switch(e){case"mgsam":return"bg-orange-100 text-orange-800 border border-orange-200";case"metaanalizgroup":return"bg-blue-100 text-blue-800 border border-blue-200";case"metaanaliz-musavirlik":return"bg-green-100 text-green-800 border border-green-200";default:return"bg-gray-100 text-gray-800 border border-gray-200"}},M=e=>{switch(e){case"mgsam":return"MGSAM";case"metaanalizgroup":return"Meta Analiz Group";case"metaanaliz-musavirlik":return"Meta Analiz M\xfcşavirlik";default:return(null==e?void 0:e.toUpperCase())||"Bilinmeyen"}},R=e.filter(e=>"all"===w||e.source===w);return((0,s.useEffect)(()=>{z()},[]),r)?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsx)("div",{className:"text-lg",children:"Y\xfckleniyor..."})}):l?(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-red-500",children:["Hata: ",l]})}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex space-x-2 mb-4",children:[(0,t.jsx)("button",{onClick:()=>k("metaanalizgroup"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${"metaanalizgroup"===w?"bg-blue-600 text-white shadow-lg":"text-gray-600 hover:bg-gray-100 hover:text-gray-900 border border-gray-300"}`,children:"Meta Analiz Group"}),(0,t.jsx)("button",{onClick:()=>k("metaanaliz-musavirlik"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${"metaanaliz-musavirlik"===w?"bg-green-600 text-white shadow-lg":"text-gray-600 hover:bg-gray-100 hover:text-gray-900 border border-gray-300"}`,children:"Meta Analiz M\xfcşavirlik"}),(0,t.jsx)("button",{onClick:()=>k("mgsam"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${"mgsam"===w?"bg-orange-600 text-white shadow-lg":"text-gray-600 hover:bg-gray-100 hover:text-gray-900 border border-gray-300"}`,children:"MGSAM"})]}),(0,t.jsx)("div",{className:"grid gap-3",children:R.map(e=>(0,t.jsxs)(d,{className:`${!e.is_read?"border-blue-200 bg-blue-50":""} max-w-4xl`,children:[(0,t.jsx)(c,{className:"pb-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-1 min-w-0",children:[(0,t.jsx)(o,{className:"text-base truncate",children:e.name}),(0,t.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${S(e.source||"metaanalizgroup")}`,children:M(e.source||"metaanalizgroup")}),!e.is_read&&(0,t.jsx)(g,{className:"text-xs bg-red-100 text-red-800 flex-shrink-0",children:"Yeni"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[E(e.status),(0,t.jsxs)("div",{className:"flex gap-1",children:[!e.is_read&&(0,t.jsx)(x,{size:"sm",variant:"outline",onClick:()=>C(e.id),className:"h-7 w-7 p-0",children:(0,t.jsx)(u.A,{className:"h-3 w-3"})}),(0,t.jsx)(x,{size:"sm",variant:"outline",onClick:()=>A(e.id),className:"h-7 w-7 p-0 text-red-600 hover:text-red-700",children:(0,t.jsx)(h.A,{className:"h-3 w-3"})})]})]})]})}),(0,t.jsxs)(m,{className:"space-y-2 pt-2",children:[(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"h-3 w-3 text-gray-500"}),(0,t.jsx)("span",{className:"font-medium",children:e.phone})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(f.A,{className:"h-3 w-3 text-gray-500"}),(0,t.jsx)("span",{className:"text-gray-600",children:e.preferred_time})]})]}),e.message&&(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(b.A,{className:"h-3 w-3 text-gray-500 mt-1 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-sm text-gray-700 line-clamp-2",children:e.message})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center text-xs text-gray-500 pt-1 border-t",children:[(0,t.jsx)("div",{className:"flex items-center gap-2"}),(0,t.jsx)("span",{children:_(e.created_at)})]})]})]},e.id))}),0===e.length&&(0,t.jsx)("div",{className:"text-center py-12",children:(0,t.jsx)("p",{className:"text-gray-500",children:"Hen\xfcz geri arama talebi bulunmuyor."})}),j>1&&(0,t.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,t.jsx)(x,{variant:"outline",onClick:()=>z(y-1),disabled:1===y,children:"\xd6nceki"}),(0,t.jsxs)("span",{className:"flex items-center px-4",children:[y," / ",j]}),(0,t.jsx)(x,{variant:"outline",onClick:()=>z(y+1),disabled:y===j,children:"Sonraki"})]})]})}},8412:(e,a,r)=>{Promise.resolve().then(r.bind(r,750))}},e=>{var a=a=>e(e.s=a);e.O(0,[243,441,684,358],()=>a(8412)),_N_E=e.O()}]);