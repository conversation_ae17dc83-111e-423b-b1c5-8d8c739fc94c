(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{2347:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,9243,23)),Promise.resolve().then(n.t.bind(n,4699,23)),Promise.resolve().then(n.t.bind(n,9324,23))},2374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{cancelIdleCallback:function(){return r},requestIdleCallback:function(){return n}});let n="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},r="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return a}});let n={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},r=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function l(e){return["async","defer","noModule"].includes(e)}function a(e,t){for(let[a,i]of Object.entries(t)){if(!t.hasOwnProperty(a)||r.includes(a)||void 0===i)continue;let o=n[a]||a.toLowerCase();"SCRIPT"===e.tagName&&l(o)?e[o]=!!i:e.setAttribute(o,String(i)),(!1===i||"SCRIPT"===e.tagName&&l(o)&&(!i||"false"===i))&&(e.setAttribute(o,""),e.removeAttribute(o))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4699:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},9243:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},handleClientScriptLoad:function(){return _},initScriptLoader:function(){return b}});let r=n(8229),l=n(6966),a=n(5155),i=r._(n(7650)),o=l._(n(2115)),s=n(2830),u=n(2714),c=n(2374),d=new Map,f=new Set,p=e=>{if(i.default.preinit)return void e.forEach(e=>{i.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}},y=e=>{let{src:t,id:n,onLoad:r=()=>{},onReady:l=null,dangerouslySetInnerHTML:a,children:i="",strategy:o="afterInteractive",onError:s,stylesheets:c}=e,y=n||t;if(y&&f.has(y))return;if(d.has(t)){f.add(y),d.get(t).then(r,s);return}let _=()=>{l&&l(),f.add(y)},b=document.createElement("script"),h=new Promise((e,t)=>{b.addEventListener("load",function(t){e(),r&&r.call(this,t),_()}),b.addEventListener("error",function(e){t(e)})}).catch(function(e){s&&s(e)});a?(b.innerHTML=a.__html||"",_()):i?(b.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",_()):t&&(b.src=t,d.set(t,h)),(0,u.setAttributesFromProps)(b,e),"worker"===o&&b.setAttribute("type","text/partytown"),b.setAttribute("data-nscript",o),c&&p(c),document.body.appendChild(b)};function _(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>y(e))}):y(e)}function b(e){e.forEach(_),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function h(e){let{id:t,src:n="",onLoad:r=()=>{},onReady:l=null,strategy:u="afterInteractive",onError:d,stylesheets:p,..._}=e,{updateScripts:b,scripts:h,getIsSsr:g,appDir:m,nonce:v}=(0,o.useContext)(s.HeadManagerContext),O=(0,o.useRef)(!1);(0,o.useEffect)(()=>{let e=t||n;O.current||(l&&e&&f.has(e)&&l(),O.current=!0)},[l,t,n]);let I=(0,o.useRef)(!1);if((0,o.useEffect)(()=>{if(!I.current){if("afterInteractive"===u)y(e);else"lazyOnload"===u&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>y(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>y(e))}));I.current=!0}},[e,u]),("beforeInteractive"===u||"worker"===u)&&(b?(h[u]=(h[u]||[]).concat([{id:t,src:n,onLoad:r,onReady:l,onError:d,..._}]),b(h)):g&&g()?f.add(t||n):g&&!g()&&y(e)),m){if(p&&p.forEach(e=>{i.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)if(!n)return _.dangerouslySetInnerHTML&&(_.children=_.dangerouslySetInnerHTML.__html,delete _.dangerouslySetInnerHTML),(0,a.jsx)("script",{nonce:v,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{..._,id:t}])+")"}});else return i.default.preload(n,_.integrity?{as:"script",integrity:_.integrity,nonce:v,crossOrigin:_.crossOrigin}:{as:"script",nonce:v,crossOrigin:_.crossOrigin}),(0,a.jsx)("script",{nonce:v,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{..._,id:t}])+")"}});"afterInteractive"===u&&n&&i.default.preload(n,_.integrity?{as:"script",integrity:_.integrity,nonce:v,crossOrigin:_.crossOrigin}:{as:"script",nonce:v,crossOrigin:_.crossOrigin})}return null}Object.defineProperty(h,"__nextScript",{value:!0});let g=h;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9324:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[722,441,684,358],()=>t(2347)),_N_E=e.O()}]);