(()=>{var e={};e.id=103,e.ids=[103],e.modules={402:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(687),s=a(3210),i=a(3613),n=a(3861),l=a(5336),o=a(8730),d=a(8887),c=a(3931),m=a(8340),x=a(228),u=a(3964),h=a(8233);function p(){let[e,t]=(0,s.useState)([]),[a,p]=(0,s.useState)(!0),[g,b]=(0,s.useState)(!1),[y,v]=(0,s.useState)("all"),[k,f]=(0,s.useState)("unread"),[w,j]=(0,s.useState)(!0),N=async(e,a)=>{try{(await fetch("/api/admin/contacts.php",{method:"PATCH",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({id:e,action:"mark_read",source_table:a})})).ok&&t(t=>t.map(t=>t.id===e?{...t,status:"read"}:t))}catch(e){}},A=async e=>{if(confirm("Bu mesajı silmek istediğinizden emin misiniz?"))try{(await fetch(`/api/admin/contacts.php?id=${e}`,{method:"DELETE",credentials:"include"})).ok&&t(t=>t.filter(t=>t.id!==e))}catch(e){}},z=e=>{switch(e){case"new":return(0,r.jsx)(i.A,{className:"h-4 w-4 text-amber-500"});case"read":return(0,r.jsx)(n.A,{className:"h-4 w-4 text-blue-500"});case"replied":return(0,r.jsx)(l.A,{className:"h-4 w-4 text-emerald-500"});default:return(0,r.jsx)(o.A,{className:"h-4 w-4 text-gray-500"})}},M=e=>{switch(e){case"new":return"Yeni";case"read":return"Okundu";case"replied":return"Yanıtlandı";default:return"Bekliyor"}},_=e=>{switch(e){case"mgsam":return"bg-orange-100 text-orange-800 border border-orange-200";case"metaanalizgroup":return"bg-blue-100 text-blue-800 border border-blue-200";case"metaanaliz-musavirlik":return"bg-green-100 text-green-800 border border-green-200";default:return"bg-gray-100 text-gray-800 border border-gray-200"}},C=e=>{switch(e){case"mgsam":return"MGSAM";case"metaanaliz-group":return"Meta Analiz Group";case"metaanaliz-musavirlik":return"Meta Analiz M\xfcşavirlik";default:return e?.toUpperCase()||"Bilinmeyen"}},$=e.filter(e=>("all"===y||e.site_code===y)&&"new"===e.status),P=e.filter(e=>"new"===e.status).length;return a?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:`animate-spin rounded-full h-12 w-12 border-b-2 ${g?"border-blue-400":"border-blue-600"}`})}):(0,r.jsx)("div",{className:`min-h-screen transition-colors duration-300 ${g?"bg-gray-900":"bg-gray-50"}`,children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[(0,r.jsx)("div",{className:"flex space-x-1",children:(0,r.jsxs)("button",{onClick:()=>f("unread"),className:`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${"unread"===k?"bg-blue-600 text-white shadow-lg":g?"text-gray-300 hover:bg-gray-700 hover:text-white":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"}`,children:["Okunmamış ",P>0&&`(${P})`]})}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>v("metaanaliz-group"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${"metaanaliz-group"===y?"bg-blue-600 text-white shadow-lg":g?"text-gray-300 hover:bg-gray-700 hover:text-white border border-gray-600":"text-gray-600 hover:bg-gray-100 hover:text-gray-900 border border-gray-300"}`,children:"Meta Analiz Group"}),(0,r.jsx)("button",{onClick:()=>v("metaanaliz-musavirlik"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${"metaanaliz-musavirlik"===y?"bg-green-600 text-white shadow-lg":g?"text-gray-300 hover:bg-gray-700 hover:text-white border border-gray-600":"text-gray-600 hover:bg-gray-100 hover:text-gray-900 border border-gray-300"}`,children:"Meta Analiz M\xfcşavirlik"}),(0,r.jsx)("button",{onClick:()=>v("mgsam"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${"mgsam"===y?"bg-orange-600 text-white shadow-lg":g?"text-gray-300 hover:bg-gray-700 hover:text-white border border-gray-600":"text-gray-600 hover:bg-gray-100 hover:text-gray-900 border border-gray-300"}`,children:"MGSAM"}),(0,r.jsx)("button",{onClick:()=>v("mgsam"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${"mgsam"===y?"bg-orange-600 text-white shadow-lg":g?"text-gray-300 hover:bg-gray-700 hover:text-white border border-gray-600":"text-gray-600 hover:bg-gray-100 hover:text-gray-900 border border-gray-300"}`,children:"MGSAM"})]})]}),(0,r.jsx)("div",{className:`rounded-xl shadow-sm border ${g?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:(0,r.jsx)("div",{className:"p-6",children:0===$.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(d.A,{className:`mx-auto h-12 w-12 mb-4 ${g?"text-gray-600":"text-gray-400"}`}),(0,r.jsx)("p",{className:`text-lg font-medium ${g?"text-gray-300":"text-gray-600"}`,children:"unread"===k?"Okunmamış mesaj bulunmuyor":"Hen\xfcz mesaj bulunmuyor"})]}):(0,r.jsx)("div",{className:"space-y-3",children:$.map(e=>(0,r.jsx)("div",{className:`p-3 rounded-lg border transition-all duration-200 hover:shadow-md max-w-4xl ${g?"bg-gray-700/50 border-gray-600 hover:border-gray-500":"bg-gray-50 border-gray-200 hover:border-gray-300"}`,children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-start sm:justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("h3",{className:`font-medium text-sm ${g?"text-white":"text-gray-900"}`,children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${_(e.source||"metaanalizgroup")}`,children:C(e.source||"metaanalizgroup")}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[z(e.status),(0,r.jsx)("span",{className:`text-xs font-medium ${g?"text-gray-400":"text-gray-600"}`,children:M(e.status)})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3 mb-2 text-xs",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1 min-w-0",children:[(0,r.jsx)(c.A,{className:`h-3 w-3 flex-shrink-0 ${g?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:`truncate ${g?"text-gray-300":"text-gray-600"}`,children:e.email})]}),e.phone&&(0,r.jsxs)("div",{className:"flex items-center space-x-1 min-w-0",children:[(0,r.jsx)(m.A,{className:`h-3 w-3 flex-shrink-0 ${g?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:`truncate ${g?"text-gray-300":"text-gray-600"}`,children:e.phone})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 min-w-0",children:[(0,r.jsx)(x.A,{className:`h-3 w-3 flex-shrink-0 ${g?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:`truncate ${g?"text-gray-300":"text-gray-600"}`,children:new Date(e.created_at).toLocaleDateString("tr-TR")})]})]}),(0,r.jsx)("div",{className:"mb-1",children:(0,r.jsxs)("p",{className:`font-medium text-xs break-words ${g?"text-gray-200":"text-gray-800"}`,children:["Konu: ",(0,r.jsx)("span",{className:"break-all",children:e.subject})]})}),(0,r.jsx)("p",{className:`text-xs line-clamp-2 break-words ${g?"text-gray-400":"text-gray-600"}`,children:e.message})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-3 sm:mt-0 sm:ml-4",children:["new"===e.status&&(0,r.jsx)("button",{onClick:()=>N(e.id,e.source_table||"contact_messages"),className:`p-2 rounded-lg transition-colors duration-200 ${g?"text-blue-400 hover:bg-blue-900/20 hover:text-blue-300":"text-blue-600 hover:bg-blue-50 hover:text-blue-700"}`,title:"Okundu olarak işaretle",children:(0,r.jsx)(u.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>A(e.id),className:`p-2 rounded-lg transition-colors duration-200 ${g?"text-red-400 hover:bg-red-900/20 hover:text-red-300":"text-red-600 hover:bg-red-50 hover:text-red-700"}`,title:"Mesajı sil",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})]})},e.id))})})})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1994:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=a(5239),s=a(8088),i=a(6076),n=a(893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);a.d(t,l);let o={children:["",{children:["control-area",{children:["dashboard",{children:["contacts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,3328)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\contacts\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,9593)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,8014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,2608)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,9766)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\loading.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,6076)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,2366)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\contacts\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/control-area/dashboard/contacts/page",pathname:"/control-area/dashboard/contacts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2601:(e,t,a)=>{Promise.resolve().then(a.bind(a,3328))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3328:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\metaanalizgorup_website\\\\app\\\\control-area\\\\dashboard\\\\contacts\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\contacts\\page.tsx","default")},3613:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3861:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},3931:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},3964:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8233:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8730:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9225:(e,t,a)=>{Promise.resolve().then(a.bind(a,402))},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,947,722,628],()=>a(1994));module.exports=r})();