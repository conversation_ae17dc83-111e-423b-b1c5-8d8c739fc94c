(()=>{var e={};e.id=887,e.ids=[887],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1288:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>t});let t=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\metaanalizgorup_website\\\\app\\\\control-area\\\\dashboard\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\settings\\page.tsx","default")},2915:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>p});var t=a(687),s=a(3210),i=a(2688);let l=(0,i.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),d=(0,i.A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);var n=a(2953),o=a(8819);let c=(0,i.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var x=a(3143);let m=(0,i.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),g=(0,i.A)("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]);function p(){let[e,r]=(0,s.useState)(!1),[a,i]=(0,s.useState)("sites"),[p,h]=(0,s.useState)([]),[u,y]=(0,s.useState)(!0),[b,v]=(0,s.useState)(null),[j,f]=(0,s.useState)({selectedModel:"gpt-4o",systemPrompt:"Sen yardımcı bir AI asistanısın. T\xfcrk\xe7e olarak profesyonel ve yararlı yanıtlar veriyorsun.",temperature:.7,maxTokens:2e3,openaiApiKey:"",claudeApiKey:"",geminiApiKey:""}),[N]=(0,s.useState)([{id:"gpt-4o",name:"GPT-4o",provider:"openai",description:"En gelişmiş GPT-4 modeli",isActive:!0},{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"openai",description:"Hızlı ve g\xfc\xe7l\xfc GPT-4",isActive:!0},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"openai",description:"Hızlı ve ekonomik model",isActive:!0},{id:"claude-3-5-sonnet",name:"Claude 3.5 Sonnet",provider:"claude",description:"En gelişmiş Claude modeli",isActive:!0},{id:"claude-3-opus",name:"Claude 3 Opus",provider:"claude",description:"G\xfc\xe7l\xfc analitik yetenekler",isActive:!0},{id:"claude-3-haiku",name:"Claude 3 Haiku",provider:"claude",description:"Hızlı ve verimli model",isActive:!0},{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",provider:"gemini",description:"Google'ın en gelişmiş modeli",isActive:!0},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",provider:"gemini",description:"Hızlı ve verimli Gemini",isActive:!0},{id:"gemini-1.0-pro",name:"Gemini 1.0 Pro",provider:"gemini",description:"G\xfcvenilir Gemini modeli",isActive:!0}]),k=async()=>{try{let e=await fetch("/api/admin/sites.php"),r=await e.json();r.success&&h(r.data.sites)}catch(e){}finally{y(!1)}},w=async e=>{try{let r=await fetch("/api/admin/sites.php",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),a=await r.json();a.success?(k(),v(null)):alert("Hata: "+a.message)}catch(e){alert("G\xfcncelleme sırasında hata oluştu.")}},A=e=>{switch(e){case"metaanaliz-group":return(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"Meta Analiz Group"});case"metaanaliz-musavirlik":return(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Meta Analiz M\xfcşavirlik"});default:return(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:e})}};return u?(0,t.jsx)("div",{className:`min-h-screen flex items-center justify-center ${e?"bg-gray-900":"bg-gray-50"}`,children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,t.jsx)("div",{className:`min-h-screen transition-colors duration-300 ${e?"bg-gray-900":"bg-gray-50"}`,children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:`text-3xl font-bold ${e?"text-white":"text-gray-900"}`,children:"Ayarlar"}),(0,t.jsx)("p",{className:`mt-2 text-sm ${e?"text-gray-400":"text-gray-600"}`,children:"Sistemdeki siteleri y\xf6netin ve AI ayarlarını yapılandırın"})]}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)("div",{className:`border-b ${e?"border-gray-700":"border-gray-200"}`,children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,t.jsx)("button",{onClick:()=>i("sites"),className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${"sites"===a?e?"border-blue-400 text-blue-400":"border-blue-500 text-blue-600":e?"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(l,{className:"w-4 h-4 mr-2"}),"Site Y\xf6netimi"]})}),(0,t.jsx)("button",{onClick:()=>i("ai"),className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${"ai"===a?e?"border-blue-400 text-blue-400":"border-blue-500 text-blue-600":e?"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d,{className:"w-4 h-4 mr-2"}),"AI Ayarları"]})})]})})}),"sites"===a&&(0,t.jsx)("div",{className:`rounded-2xl shadow-lg border transition-all duration-300 ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsxs)("table",{className:"w-full",children:[(0,t.jsx)("thead",{className:`${e?"bg-gray-700":"bg-gray-50"}`,children:(0,t.jsxs)("tr",{children:[(0,t.jsx)("th",{className:`px-6 py-4 text-left text-sm font-semibold ${e?"text-gray-200":"text-gray-900"}`,children:"Site"}),(0,t.jsx)("th",{className:`px-6 py-4 text-left text-sm font-semibold ${e?"text-gray-200":"text-gray-900"}`,children:"URL"}),(0,t.jsx)("th",{className:`px-6 py-4 text-left text-sm font-semibold ${e?"text-gray-200":"text-gray-900"}`,children:"A\xe7ıklama"}),(0,t.jsx)("th",{className:`px-6 py-4 text-left text-sm font-semibold ${e?"text-gray-200":"text-gray-900"}`,children:"Durum"}),(0,t.jsx)("th",{className:`px-6 py-4 text-left text-sm font-semibold ${e?"text-gray-200":"text-gray-900"}`,children:"İşlemler"})]})}),(0,t.jsx)("tbody",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:p.map(r=>(0,t.jsxs)("tr",{className:`${e?"hover:bg-gray-700":"hover:bg-gray-50"} transition-colors duration-200`,children:[(0,t.jsx)("td",{className:`px-6 py-4 text-sm ${e?"text-gray-300":"text-gray-900"}`,children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(l,{className:"h-5 w-5 text-gray-400"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:r.site_name}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:A(r.site_code)})]})]})}),(0,t.jsx)("td",{className:`px-6 py-4 text-sm ${e?"text-gray-300":"text-gray-900"}`,children:b?.id===r.id?(0,t.jsx)("input",{type:"url",value:b.site_url,onChange:e=>v({...b,site_url:e.target.value}),className:`w-full p-2 rounded border ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`}):(0,t.jsxs)("a",{href:r.site_url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-1 text-blue-600 hover:text-blue-800",children:[(0,t.jsx)("span",{children:r.site_url}),(0,t.jsx)(n.A,{className:"h-3 w-3"})]})}),(0,t.jsx)("td",{className:`px-6 py-4 text-sm ${e?"text-gray-300":"text-gray-900"}`,children:b?.id===r.id?(0,t.jsx)("textarea",{value:b.description,onChange:e=>v({...b,description:e.target.value}),className:`w-full p-2 rounded border ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`,rows:2}):(0,t.jsx)("div",{className:"max-w-xs truncate",title:r.description,children:r.description})}),(0,t.jsx)("td",{className:`px-6 py-4 text-sm ${e?"text-gray-300":"text-gray-900"}`,children:b?.id===r.id?(0,t.jsxs)("select",{value:b.is_active?"1":"0",onChange:e=>v({...b,is_active:"1"===e.target.value}),className:`p-2 rounded border ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`,children:[(0,t.jsx)("option",{value:"1",children:"Aktif"}),(0,t.jsx)("option",{value:"0",children:"Pasif"})]}):(0,t.jsx)("span",{className:`px-2 py-1 rounded text-xs font-medium ${r.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:r.is_active?"Aktif":"Pasif"})}),(0,t.jsx)("td",{className:"px-6 py-4 text-sm",children:(0,t.jsx)("div",{className:"flex items-center space-x-2",children:b?.id===r.id?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("button",{onClick:()=>w(b),className:`p-2 rounded-lg transition-colors duration-200 ${e?"text-green-400 hover:bg-green-900/20 hover:text-green-300":"text-green-600 hover:bg-green-50 hover:text-green-700"}`,title:"Kaydet",children:(0,t.jsx)(o.A,{className:"h-4 w-4"})}),(0,t.jsx)("button",{onClick:()=>v(null),className:`p-2 rounded-lg transition-colors duration-200 ${e?"text-red-400 hover:bg-red-900/20 hover:text-red-300":"text-red-600 hover:bg-red-50 hover:text-red-700"}`,title:"İptal",children:(0,t.jsx)(c,{className:"h-4 w-4"})})]}):(0,t.jsx)("button",{onClick:()=>v(r),className:`p-2 rounded-lg transition-colors duration-200 ${e?"text-blue-400 hover:bg-blue-900/20 hover:text-blue-300":"text-blue-600 hover:bg-blue-50 hover:text-blue-700"}`,title:"D\xfczenle",children:(0,t.jsx)(x.A,{className:"h-4 w-4"})})})})]},r.id))})]})})}),"ai"===a&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:`rounded-2xl shadow-lg border transition-all duration-300 ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[(0,t.jsxs)("div",{className:`px-6 py-4 border-b ${e?"border-gray-700":"border-gray-200"}`,children:[(0,t.jsx)("h3",{className:`text-lg font-semibold ${e?"text-white":"text-gray-900"}`,children:"AI Model Se\xe7imi"}),(0,t.jsx)("p",{className:`mt-1 text-sm ${e?"text-gray-400":"text-gray-600"}`,children:"Kullanılacak AI modelini se\xe7in"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:`text-sm font-medium mb-3 flex items-center ${e?"text-gray-300":"text-gray-700"}`,children:[(0,t.jsx)(m,{className:"w-4 h-4 mr-2"}),"OpenAI"]}),(0,t.jsx)("div",{className:"space-y-2",children:N.filter(e=>"openai"===e.provider).map(r=>(0,t.jsxs)("label",{className:`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${j.selectedModel===r.id?e?"border-blue-500 bg-blue-500/10":"border-blue-500 bg-blue-50":e?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300"}`,children:[(0,t.jsx)("input",{type:"radio",name:"aiModel",value:r.id,checked:j.selectedModel===r.id,onChange:e=>f(r=>({...r,selectedModel:e.target.value})),className:"sr-only"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:`text-sm font-medium ${e?"text-white":"text-gray-900"}`,children:r.name}),(0,t.jsx)("div",{className:`text-xs ${e?"text-gray-400":"text-gray-500"}`,children:r.description})]}),j.selectedModel===r.id&&(0,t.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center",children:(0,t.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})]},r.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:`text-sm font-medium mb-3 flex items-center ${e?"text-gray-300":"text-gray-700"}`,children:[(0,t.jsx)(g,{className:"w-4 h-4 mr-2"}),"Claude"]}),(0,t.jsx)("div",{className:"space-y-2",children:N.filter(e=>"claude"===e.provider).map(r=>(0,t.jsxs)("label",{className:`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${j.selectedModel===r.id?e?"border-blue-500 bg-blue-500/10":"border-blue-500 bg-blue-50":e?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300"}`,children:[(0,t.jsx)("input",{type:"radio",name:"aiModel",value:r.id,checked:j.selectedModel===r.id,onChange:e=>f(r=>({...r,selectedModel:e.target.value})),className:"sr-only"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:`text-sm font-medium ${e?"text-white":"text-gray-900"}`,children:r.name}),(0,t.jsx)("div",{className:`text-xs ${e?"text-gray-400":"text-gray-500"}`,children:r.description})]}),j.selectedModel===r.id&&(0,t.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center",children:(0,t.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})]},r.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:`text-sm font-medium mb-3 flex items-center ${e?"text-gray-300":"text-gray-700"}`,children:[(0,t.jsx)(d,{className:"w-4 h-4 mr-2"}),"Gemini"]}),(0,t.jsx)("div",{className:"space-y-2",children:N.filter(e=>"gemini"===e.provider).map(r=>(0,t.jsxs)("label",{className:`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${j.selectedModel===r.id?e?"border-blue-500 bg-blue-500/10":"border-blue-500 bg-blue-50":e?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300"}`,children:[(0,t.jsx)("input",{type:"radio",name:"aiModel",value:r.id,checked:j.selectedModel===r.id,onChange:e=>f(r=>({...r,selectedModel:e.target.value})),className:"sr-only"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:`text-sm font-medium ${e?"text-white":"text-gray-900"}`,children:r.name}),(0,t.jsx)("div",{className:`text-xs ${e?"text-gray-400":"text-gray-500"}`,children:r.description})]}),j.selectedModel===r.id&&(0,t.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center",children:(0,t.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})]},r.id))})]})]})})]}),(0,t.jsxs)("div",{className:`rounded-2xl shadow-lg border transition-all duration-300 ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[(0,t.jsxs)("div",{className:`px-6 py-4 border-b ${e?"border-gray-700":"border-gray-200"}`,children:[(0,t.jsx)("h3",{className:`text-lg font-semibold ${e?"text-white":"text-gray-900"}`,children:"API Anahtarları"}),(0,t.jsx)("p",{className:`mt-1 text-sm ${e?"text-gray-400":"text-gray-600"}`,children:"AI servislerine erişim i\xe7in API anahtarlarınızı girin"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:`flex items-center text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:[(0,t.jsx)(m,{className:"w-4 h-4 mr-2"}),"OpenAI API Key"]}),(0,t.jsx)("input",{type:"password",value:j.openaiApiKey,onChange:e=>f(r=>({...r,openaiApiKey:e.target.value})),placeholder:"sk-...",className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${e?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:`flex items-center text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:[(0,t.jsx)(g,{className:"w-4 h-4 mr-2"}),"Claude API Key"]}),(0,t.jsx)("input",{type:"password",value:j.claudeApiKey,onChange:e=>f(r=>({...r,claudeApiKey:e.target.value})),placeholder:"sk-ant-...",className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${e?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:`flex items-center text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:[(0,t.jsx)(d,{className:"w-4 h-4 mr-2"}),"Gemini API Key"]}),(0,t.jsx)("input",{type:"password",value:j.geminiApiKey,onChange:e=>f(r=>({...r,geminiApiKey:e.target.value})),placeholder:"AIza...",className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${e?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`})]})]})})]}),(0,t.jsxs)("div",{className:`rounded-2xl shadow-lg border transition-all duration-300 ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[(0,t.jsxs)("div",{className:`px-6 py-4 border-b ${e?"border-gray-700":"border-gray-200"}`,children:[(0,t.jsx)("h3",{className:`text-lg font-semibold ${e?"text-white":"text-gray-900"}`,children:"System Prompt Ayarları"}),(0,t.jsx)("p",{className:`mt-1 text-sm ${e?"text-gray-400":"text-gray-600"}`,children:"AI'nın davranışını belirleyen sistem talimatları"})]}),(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:`block text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:"System Prompt"}),(0,t.jsx)("textarea",{value:j.systemPrompt,onChange:e=>f(r=>({...r,systemPrompt:e.target.value})),rows:6,className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${e?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`,placeholder:"AI'nın nasıl davranacağını belirleyen talimatları yazın..."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:`block text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:["Temperature (",j.temperature,")"]}),(0,t.jsx)("input",{type:"range",min:"0",max:"2",step:"0.1",value:j.temperature,onChange:e=>f(r=>({...r,temperature:parseFloat(e.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"}),(0,t.jsx)("div",{className:`text-xs mt-1 ${e?"text-gray-400":"text-gray-500"}`,children:"D\xfcş\xfck: Daha tutarlı, Y\xfcksek: Daha yaratıcı"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:`block text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:"Max Tokens"}),(0,t.jsx)("input",{type:"number",min:"100",max:"4000",value:j.maxTokens,onChange:e=>f(r=>({...r,maxTokens:parseInt(e.target.value)})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`}),(0,t.jsx)("div",{className:`text-xs mt-1 ${e?"text-gray-400":"text-gray-500"}`,children:"Maksimum yanıt uzunluğu"})]})]})]})})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{onClick:()=>{alert("AI ayarları kaydedildi!")},className:"px-6 py-2 rounded-lg font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Ayarları Kaydet"]})})})]})]})})}},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3143:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(2688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4994:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>o,routeModule:()=>x,tree:()=>n});var t=a(5239),s=a(8088),i=a(6076),l=a(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(r,d);let n={children:["",{children:["control-area",{children:["dashboard",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,1288)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,9593)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,8014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,2608)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,9766)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\loading.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,6076)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,2366)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\settings\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/control-area/dashboard/settings/page",pathname:"/control-area/dashboard/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},7541:(e,r,a)=>{Promise.resolve().then(a.bind(a,1288))},8819:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(2688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9397:(e,r,a)=>{Promise.resolve().then(a.bind(a,2915))},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[447,947,722,628],()=>a(4994));module.exports=t})();