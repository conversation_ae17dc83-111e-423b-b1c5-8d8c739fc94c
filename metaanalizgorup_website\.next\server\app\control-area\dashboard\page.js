(()=>{var e={};e.id=1,e.ids=[1],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2844:(e,r,a)=>{Promise.resolve().then(a.bind(a,3006))},3006:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>t});let t=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\metaanalizgorup_website\\\\app\\\\control-area\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\page.tsx","default")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3048:(e,r,a)=>{"use strict";a.r(r),a.d(r,{GlobalError:()=>l.default,__next_app__:()=>m,pages:()=>o,routeModule:()=>c,tree:()=>n});var t=a(5239),s=a(8088),l=a(6076),i=a(893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(r,d);let n={children:["",{children:["control-area",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,3006)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,9593)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,8014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,2608)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,9766)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\loading.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,6076)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,2366)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},c=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/control-area/dashboard/page",pathname:"/control-area/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3613:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3861:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},5336:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6396:(e,r,a)=>{Promise.resolve().then(a.bind(a,7996))},7996:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>x});var t=a(687),s=a(3210),l=a(5814),i=a.n(l),d=a(3613),n=a(8340),o=a(8730),m=a(3861),c=a(5336);function x(){let[e,r]=(0,s.useState)(!1),[a,l]=(0,s.useState)({unread_contacts:0,unread_callbacks:0,unread_meetings:0,total_contacts:0,total_callbacks:0,total_meetings:0,pending_meetings:0,completed_meetings:0,confirmed_meetings:0}),[x,b]=(0,s.useState)(!0),[u,p]=(0,s.useState)(!0),h=[{title:"Okunmamış Mesajlar",value:a.unread_contacts,icon:d.A,gradient:e?"from-slate-600 to-slate-800":"from-slate-500 to-slate-600",bgColor:e?"bg-slate-900/20":"bg-slate-50",iconColor:e?"text-slate-400":"text-slate-600",href:"/control-area/dashboard/contacts"},{title:"Okunmamış Geri Arama",value:a.unread_callbacks,icon:n.A,gradient:e?"from-blue-600 to-blue-800":"from-blue-500 to-blue-600",bgColor:e?"bg-blue-900/20":"bg-blue-50",iconColor:e?"text-blue-400":"text-blue-600",href:"/control-area/dashboard/callbacks"},{title:"Okunmamış Toplantılar",value:a.unread_meetings,icon:o.A,gradient:e?"from-emerald-600 to-emerald-800":"from-emerald-500 to-emerald-600",bgColor:e?"bg-emerald-900/20":"bg-emerald-50",iconColor:e?"text-emerald-400":"text-emerald-600",href:"/control-area/dashboard/meetings"}];return x?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:`animate-spin rounded-full h-12 w-12 border-b-2 ${e?"border-blue-400":"border-blue-600"}`})}):(0,t.jsx)("div",{className:`min-h-screen transition-colors duration-300 ${e?"bg-gray-900":"bg-gray-50"}`,children:(0,t.jsxs)("div",{className:"p-6 space-y-8",children:[(0,t.jsx)("div",{className:"grid grid-cols-2 gap-4 md:gap-6",children:h.map((r,a)=>(0,t.jsx)(i(),{href:r.href,className:`group relative overflow-hidden rounded-xl border transition-all duration-300 hover:scale-[1.02] ${e?"bg-gray-800 border-gray-700 hover:border-gray-600":"bg-white border-gray-200 hover:border-gray-300"}`,children:(0,t.jsx)("div",{className:"relative p-6",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:`p-3 rounded-xl ${r.bgColor}`,children:(0,t.jsx)(r.icon,{className:`h-6 w-6 ${r.iconColor}`})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:`text-sm font-medium ${e?"text-gray-400":"text-gray-600"}`,children:r.title}),(0,t.jsx)("p",{className:`text-2xl font-bold ${e?"text-white":"text-gray-900"}`,children:r.value})]})]})})})},a))}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6",children:[(0,t.jsx)("div",{className:`rounded-xl border ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h2",{className:`text-lg font-bold ${e?"text-white":"text-gray-900"}`,children:"Son İletişim Mesajları"}),(0,t.jsxs)(i(),{href:"/control-area/dashboard/contacts",className:`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${e?"text-blue-400 hover:bg-blue-900/20 hover:text-blue-300":"text-blue-600 hover:bg-blue-50 hover:text-blue-700"}`,children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"T\xfcm\xfcn\xfc G\xf6r"]})]}),(0,t.jsx)("div",{className:"space-y-4",children:a.unread_contacts>0?(0,t.jsxs)("div",{className:`flex items-center p-4 rounded-xl border ${e?"bg-slate-900/20 border-slate-800 text-slate-300":"bg-slate-50 border-slate-200 text-slate-800"}`,children:[(0,t.jsx)("div",{className:`p-2 rounded-lg ${e?"bg-slate-800/30":"bg-slate-100"}`,children:(0,t.jsx)(d.A,{className:`h-5 w-5 ${e?"text-slate-400":"text-slate-600"}`})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsxs)("p",{className:`text-sm font-semibold ${e?"text-slate-200":"text-slate-900"}`,children:[a.unread_contacts," yeni mesaj var"]}),(0,t.jsx)("p",{className:`text-xs ${e?"text-slate-400":"text-slate-700"}`,children:"İncelemeniz gerekiyor"})]})]}):(0,t.jsxs)("div",{className:`flex items-center p-4 rounded-xl border ${e?"bg-emerald-900/20 border-emerald-800 text-emerald-300":"bg-emerald-50 border-emerald-200 text-emerald-800"}`,children:[(0,t.jsx)("div",{className:`p-2 rounded-lg ${e?"bg-emerald-800/30":"bg-emerald-100"}`,children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-emerald-500"})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:`text-sm font-semibold ${e?"text-emerald-200":"text-emerald-900"}`,children:"T\xfcm mesajlar incelendi"}),(0,t.jsx)("p",{className:`text-xs ${e?"text-emerald-400":"text-emerald-700"}`,children:"Harika iş! \uD83C\uDF89"})]})]})})]})}),(0,t.jsx)("div",{className:`rounded-xl border ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:(0,t.jsxs)("div",{className:"p-5",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h2",{className:`text-lg font-bold ${e?"text-white":"text-gray-900"}`,children:"Toplantı Durumu"}),(0,t.jsxs)(i(),{href:"/control-area/dashboard/meetings",className:`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${e?"text-blue-400 hover:bg-blue-900/20 hover:text-blue-300":"text-blue-600 hover:bg-blue-50 hover:text-blue-700"}`,children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"T\xfcm\xfcn\xfc G\xf6r"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:`flex items-center justify-between p-4 rounded-xl border ${e?"bg-emerald-900/20 border-emerald-800":"bg-emerald-50 border-emerald-200"}`,children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`p-2 rounded-lg ${e?"bg-emerald-800/30":"bg-emerald-100"}`,children:(0,t.jsx)(o.A,{className:`h-5 w-5 ${e?"text-emerald-400":"text-emerald-600"}`})}),(0,t.jsx)("span",{className:`ml-3 text-sm font-semibold ${e?"text-emerald-200":"text-emerald-900"}`,children:"Bekleyen"})]}),(0,t.jsx)("span",{className:`text-2xl font-bold ${e?"text-emerald-300":"text-emerald-600"}`,children:a.pending_meetings})]}),(0,t.jsxs)("div",{className:`flex items-center justify-between p-4 rounded-xl border ${e?"bg-emerald-900/20 border-emerald-800":"bg-emerald-50 border-emerald-200"}`,children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`p-2 rounded-lg ${e?"bg-emerald-800/30":"bg-emerald-100"}`,children:(0,t.jsx)(c.A,{className:"h-5 w-5 text-emerald-500"})}),(0,t.jsx)("span",{className:`ml-3 text-sm font-semibold ${e?"text-emerald-200":"text-emerald-900"}`,children:"Tamamlanan"})]}),(0,t.jsx)("span",{className:`text-2xl font-bold ${e?"text-emerald-300":"text-emerald-600"}`,children:a.completed_meetings})]})]})]})})]})]})})}},8730:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[447,947,722,628],()=>a(3048));module.exports=t})();