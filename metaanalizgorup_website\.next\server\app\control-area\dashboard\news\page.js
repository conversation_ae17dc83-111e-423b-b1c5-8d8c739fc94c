(()=>{var e={};e.id=265,e.ids=[265],e.modules={36:(e,t,a)=>{"use strict";a.d(t,{default:()=>s.a});var r=a(9587),s=a.n(r)},674:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=a(5239),s=a(8088),i=a(6076),l=a(893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);a.d(t,n);let o={children:["",{children:["control-area",{children:["dashboard",{children:["news",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,9006)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\news\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,9593)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,8014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,2608)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,9766)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\loading.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,6076)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,2366)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\news\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/control-area/dashboard/news/page",pathname:"/control-area/dashboard/news",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1270:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>f});var r=a(687),s=a(3210),i=a(6474),l=a(5547),n=a(9270);let o=(0,a(2688).A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var d=a(7992),c=a(228),u=a(3613),x=a(5336),p=a(8730),g=a(3861),m=a(3143),h=a(8233),y=a(6085),b=a(8819);let v=(0,a(36).default)(async()=>{},{loadableGenerated:{modules:["app\\control-area\\dashboard\\news\\page.tsx -> @uiw/react-md-editor"]},ssr:!1});function f(){let[e,t]=(0,s.useState)([]),[a,f]=(0,s.useState)([]),[k,j]=(0,s.useState)([]),[w,N]=(0,s.useState)(!0),[_,A]=(0,s.useState)(!1),[$,P]=(0,s.useState)(""),[C,z]=(0,s.useState)("add"),[M,S]=(0,s.useState)(null),[D,H]=(0,s.useState)({current_page:1,total_pages:1,total_count:0,per_page:20}),[O,E]=(0,s.useState)({title:"",description:"",kategori:"",sehir:"",son_dakika:"Hayır",status:"active"}),[I,q]=(0,s.useState)(!1),[B,R]=(0,s.useState)(!1),[T,U]=(0,s.useState)(!1),L=async()=>{try{N(!0);let e=$?`&search=${encodeURIComponent($)}`:"",a=await fetch(`/api/admin/news.php?page=${D.current_page}${e}`);if(a.ok){let e=await a.json();e.success&&(t(e.data.news||[]),H(e.data.pagination))}}catch(e){}finally{N(!1)}},G=(e,t)=>{E(a=>({...a,[e]:t}))},F=async e=>{if(e.preventDefault(),!O.title.trim()||!O.description.trim())return void alert("Başlık ve i\xe7erik alanları zorunludur!");q(!0);try{let e=M?"PUT":"POST",t=M?{...O,id:M.id}:{...O,pub_date:new Date().toISOString().slice(0,19).replace("T"," ")},a=await fetch("/api/admin/news.php",{method:e,headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),r=await a.json();r.success?(alert(M?"Haber başarıyla g\xfcncellendi!":"Haber başarıyla eklendi!"),K(),z("list"),L()):alert("Hata: "+(r.message||"Bilinmeyen hata"))}catch(e){alert("Haber kaydedilirken hata oluştu!")}finally{q(!1)}},K=()=>{E({title:"",description:"",kategori:"",sehir:"",son_dakika:"Hayır",status:"active"}),S(null)},V=e=>{E({title:e.title,description:e.description,kategori:e.kategori,sehir:e.sehir,son_dakika:e.son_dakika,status:"deleted"===e.status?"inactive":e.status}),S(e),z("add")},J=async e=>{if(confirm("Bu haberi silmek istediğinizden emin misiniz?"))try{let t=await fetch(`/api/admin/news.php?id=${e}`,{method:"DELETE"}),a=await t.json();a.success?(alert("Haber başarıyla silindi!"),L()):alert("Hata: "+(a.message||"Bilinmeyen hata"))}catch(e){alert("Haber silinirken hata oluştu!")}},X=e=>{P(e),H(e=>({...e,current_page:1}))},Y=e=>{H(t=>({...t,current_page:e}))},W=async e=>{let t="title"===e?O.title:O.description;if(!t.trim())return void alert(`L\xfctfen \xf6nce ${"title"===e?"başlık":"i\xe7erik"} alanını doldurun!`);let a="title"===e?R:U;a(!0);try{let a=await fetch("/api/admin/settings.php"),r=await a.json();if(!r.success)throw Error("Site ayarları alınamadı");let s=r.data.ai_model||"gpt-3.5-turbo",i=r.data.ai_api_key;if(!i)return void alert("AI API anahtarı ayarlanmamış! L\xfctfen Site Ayarları > AI Ayarları b\xf6l\xfcm\xfcnden API anahtarını ekleyin.");let l="title"===e?`Bu haber başlığını daha \xe7ekici ve profesyonel hale getir. Sadece iyileştirilmiş başlığı d\xf6nd\xfcr: "${t}"`:`Bu haber i\xe7eriğini daha profesyonel, akıcı ve bilgilendirici hale getir. Gramer ve yazım hatalarını d\xfczelt, daha iyi bir yapı kur. Sadece iyileştirilmiş i\xe7eriği d\xf6nd\xfcr: "${t}"`,n=await fetch("/api/admin/ai-improve.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:l,model:s,api_key:i})}),o=await n.json();o.success?("title"===e?E(e=>({...e,title:o.improved_text})):E(e=>({...e,description:o.improved_text})),alert(`${"title"===e?"Başlık":"İ\xe7erik"} AI ile başarıyla iyileştirildi!`)):alert("AI iyileştirme hatası: "+(o.message||"Bilinmeyen hata"))}catch(e){alert("AI iyileştirme sırasında hata oluştu!")}finally{a(!1)}},Q=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),Z=e=>{switch(e){case"active":return"Aktif";case"inactive":return"Pasif";default:return"Bilinmeyen"}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:`border-b ${_?"border-gray-700":"border-gray-200"}`,children:(0,r.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,r.jsxs)("button",{onClick:()=>{z("add"),M||K()},className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${"add"===C?_?"border-blue-400 text-blue-400":"border-blue-500 text-blue-600":_?"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,r.jsx)(i.A,{className:"w-4 h-4 inline mr-2"}),"Yeni Haber Ekle"]}),(0,r.jsxs)("button",{onClick:()=>z("list"),className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${"list"===C?_?"border-blue-400 text-blue-400":"border-blue-500 text-blue-600":_?"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,r.jsx)(l.A,{className:"w-4 h-4 inline mr-2"}),"T\xfcm Haberler (",D.total_count,")"]})]})}),"list"===C&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:`rounded-xl p-6 ${_?"bg-gray-800":"bg-white"} shadow-sm`,children:(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-4",children:(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.A,{className:`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${_?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("input",{type:"text",placeholder:"Haber ara (başlık, i\xe7erik, haber kodu)...",value:$,onChange:e=>X(e.target.value),className:`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`})]})})})}),(0,r.jsxs)("div",{className:`rounded-xl ${_?"bg-gray-800":"bg-white"} shadow-sm overflow-hidden`,children:[w?(0,r.jsx)("div",{className:"p-8 text-center",children:(0,r.jsx)("div",{className:`text-lg ${_?"text-gray-300":"text-gray-600"}`,children:"Haberler y\xfckleniyor..."})}):0===e.length?(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)(l.A,{className:`w-12 h-12 mx-auto mb-4 ${_?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("h3",{className:`text-lg font-medium mb-2 ${_?"text-gray-300":"text-gray-900"}`,children:"Hen\xfcz haber yok"}),(0,r.jsx)("p",{className:`${_?"text-gray-400":"text-gray-500"}`,children:'İlk haberinizi eklemek i\xe7in "Yeni Haber Ekle" sekmesini kullanın.'})]}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:`${_?"bg-gray-700":"bg-gray-50"}`,children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${_?"text-gray-300":"text-gray-500"}`,children:"Haber Bilgileri"}),(0,r.jsx)("th",{className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${_?"text-gray-300":"text-gray-500"}`,children:"Kategori"}),(0,r.jsx)("th",{className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${_?"text-gray-300":"text-gray-500"}`,children:"Durum"}),(0,r.jsx)("th",{className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${_?"text-gray-300":"text-gray-500"}`,children:"İstatistik"}),(0,r.jsx)("th",{className:`px-6 py-3 text-left text-xs font-medium uppercase tracking-wider ${_?"text-gray-300":"text-gray-500"}`,children:"İşlemler"})]})}),(0,r.jsx)("tbody",{className:`divide-y ${_?"divide-gray-700":"divide-gray-200"}`,children:e.map(e=>(0,r.jsxs)("tr",{className:_?"hover:bg-gray-700":"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:`text-sm font-medium ${_?"text-white":"text-gray-900"}`,children:e.title}),(0,r.jsxs)("div",{className:`text-xs ${_?"text-gray-400":"text-gray-500"} flex items-center gap-2 mt-1`,children:[(0,r.jsx)(o,{className:"w-3 h-3"}),e.haber_kodu,e.sehir&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"w-3 h-3 ml-2"}),e.sehir]})]}),(0,r.jsxs)("div",{className:`text-xs ${_?"text-gray-400":"text-gray-500"} mt-1`,children:[(0,r.jsx)(c.A,{className:"w-3 h-3 inline mr-1"}),Q(e.pub_date)]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4",children:[(0,r.jsx)("div",{className:`text-sm ${_?"text-gray-300":"text-gray-900"}`,children:e.ust_kategori}),(0,r.jsx)("div",{className:`text-xs ${_?"text-gray-400":"text-gray-500"}`,children:e.kategori}),"Evet"===e.son_dakika&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1",children:[(0,r.jsx)(u.A,{className:"w-3 h-3 mr-1"}),"Son Dakika"]})]}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${"active"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:["active"===e.status?(0,r.jsx)(x.A,{className:"w-3 h-3 mr-1"}):(0,r.jsx)(p.A,{className:"w-3 h-3 mr-1"}),Z(e.status)]})}),(0,r.jsxs)("td",{className:"px-6 py-4",children:[(0,r.jsxs)("div",{className:`text-sm ${_?"text-gray-300":"text-gray-900"}`,children:[(0,r.jsx)(g.A,{className:"w-4 h-4 inline mr-1"}),e.view_count," g\xf6r\xfcnt\xfclenme"]}),(0,r.jsxs)("div",{className:`text-xs ${_?"text-gray-400":"text-gray-500"} flex items-center gap-2 mt-1`,children:[e.has_images&&(0,r.jsx)("span",{className:"flex items-center",children:"\uD83D\uDCF7 Resim"}),e.has_videos&&(0,r.jsx)("span",{className:"flex items-center",children:"\uD83C\uDFA5 Video"})]})]}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>V(e),className:`p-2 rounded-lg transition-colors duration-200 ${_?"text-blue-400 hover:text-blue-300 hover:bg-gray-700":"text-blue-600 hover:text-blue-700 hover:bg-gray-100"}`,title:"D\xfczenle",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>J(e.id),className:`p-2 rounded-lg transition-colors duration-200 ${_?"text-red-400 hover:text-red-300 hover:bg-gray-700":"text-red-600 hover:text-red-700 hover:bg-gray-100"}`,title:"Sil",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})})]},e.id))})]})}),D.total_pages>1&&(0,r.jsx)("div",{className:`px-6 py-4 border-t ${_?"border-gray-700 bg-gray-800":"border-gray-200 bg-gray-50"}`,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:`text-sm ${_?"text-gray-400":"text-gray-700"}`,children:[D.total_count," haberden ",(D.current_page-1)*D.per_page+1,"-",Math.min(D.current_page*D.per_page,D.total_count)," arası g\xf6steriliyor"]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("button",{onClick:()=>Y(D.current_page-1),disabled:D.current_page<=1,className:`px-3 py-1 rounded-lg text-sm transition-colors duration-200 ${D.current_page<=1?_?"text-gray-600 cursor-not-allowed":"text-gray-400 cursor-not-allowed":_?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-200"}`,children:"\xd6nceki"}),Array.from({length:Math.min(5,D.total_pages)},(e,t)=>{let a=t+1;return(0,r.jsx)("button",{onClick:()=>Y(a),className:`px-3 py-1 rounded-lg text-sm transition-colors duration-200 ${a===D.current_page?_?"bg-blue-600 text-white":"bg-blue-500 text-white":_?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-200"}`,children:a},a)}),(0,r.jsx)("button",{onClick:()=>Y(D.current_page+1),disabled:D.current_page>=D.total_pages,className:`px-3 py-1 rounded-lg text-sm transition-colors duration-200 ${D.current_page>=D.total_pages?_?"text-gray-600 cursor-not-allowed":"text-gray-400 cursor-not-allowed":_?"text-gray-300 hover:bg-gray-700":"text-gray-700 hover:bg-gray-200"}`,children:"Sonraki"})]})]})})]})]}),"add"===C&&(0,r.jsx)("div",{className:`rounded-xl ${_?"bg-gray-800":"bg-white"} shadow-sm`,children:(0,r.jsx)("div",{className:"max-w-none",children:(0,r.jsx)("div",{className:"p-6 space-y-6",children:(0,r.jsxs)("form",{onSubmit:F,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:`block text-sm font-medium mb-2 ${_?"text-gray-300":"text-gray-700"}`,children:"Başlık *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",value:O.title,onChange:e=>G("title",e.target.value),className:`w-full px-3 py-2 pr-12 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`,placeholder:"Haber başlığını girin",required:!0}),(0,r.jsx)("button",{type:"button",onClick:()=>W("title"),disabled:B||!O.title.trim(),className:`absolute right-2 top-1/2 transform -translate-y-1/2 p-1.5 rounded-md transition-colors duration-200 ${B||!O.title.trim()?"text-gray-400 cursor-not-allowed":_?"text-purple-400 hover:text-purple-300 hover:bg-gray-600":"text-purple-600 hover:text-purple-700 hover:bg-purple-50"}`,title:"AI ile İyileştir",children:B?(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:`block text-sm font-medium mb-2 ${_?"text-gray-300":"text-gray-700"}`,children:"İ\xe7erik *"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:`rounded-lg overflow-hidden ${_?"border-gray-600":"border-gray-300"}`,children:(0,r.jsx)(v,{value:O.description,onChange:e=>G("description",e||""),preview:"live",hideToolbar:!1,visibleDragbar:!1,"data-color-mode":_?"dark":"light",height:400})}),(0,r.jsx)("button",{type:"button",onClick:()=>W("content"),disabled:T||!O.description.trim(),className:`absolute right-2 top-2 p-1.5 rounded-md transition-colors duration-200 z-10 ${T||!O.description.trim()?"text-gray-400 cursor-not-allowed":_?"text-purple-400 hover:text-purple-300 hover:bg-gray-600":"text-purple-600 hover:text-purple-700 hover:bg-purple-50"}`,title:"AI ile İyileştir",children:T?(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("p",{className:`text-xs mt-2 ${_?"text-gray-400":"text-gray-500"}`,children:"Haber i\xe7eriğinizi formatlayabilirsiniz. Paragraf aralıkları, kalın punto, italik gibi formatlamalar Meta Analiz M\xfcşavirlik sitesinde de aynı şekilde g\xf6r\xfcnecektir."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:`block text-sm font-medium mb-2 ${_?"text-gray-300":"text-gray-700"}`,children:"Kategori *"}),(0,r.jsxs)("select",{value:O.kategori,onChange:e=>G("kategori",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`,required:!0,children:[(0,r.jsx)("option",{value:"",children:"Kategori Se\xe7iniz"}),a.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:`block text-sm font-medium mb-2 ${_?"text-gray-300":"text-gray-700"}`,children:"Şehir"}),(0,r.jsxs)("select",{value:O.sehir,onChange:e=>G("sehir",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`,children:[(0,r.jsx)("option",{value:"",children:"Şehir Se\xe7iniz (İsteğe Bağlı)"}),k.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:`block text-sm font-medium mb-2 ${_?"text-gray-300":"text-gray-700"}`,children:"Son Dakika"}),(0,r.jsxs)("select",{value:O.son_dakika,onChange:e=>G("son_dakika",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`,children:[(0,r.jsx)("option",{value:"Hayır",children:"Hayır"}),(0,r.jsx)("option",{value:"Evet",children:"Evet"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:`block text-sm font-medium mb-2 ${_?"text-gray-300":"text-gray-700"}`,children:"Durum"}),(0,r.jsxs)("select",{value:O.status,onChange:e=>G("status",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`,children:[(0,r.jsx)("option",{value:"active",children:"Aktif"}),(0,r.jsx)("option",{value:"inactive",children:"Pasif"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,r.jsx)("div",{children:M&&(0,r.jsx)("button",{type:"button",onClick:K,className:`text-sm ${_?"text-gray-400 hover:text-gray-300":"text-gray-600 hover:text-gray-700"}`,children:"D\xfczenlemeyi İptal Et"})}),(0,r.jsxs)("button",{type:"submit",disabled:I,className:`flex items-center px-6 py-2 rounded-lg text-white font-medium transition-colors duration-200 ${I?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"}`,children:[(0,r.jsx)(b.A,{className:"w-4 h-4 mr-2"}),I?"Kaydediliyor...":M?"G\xfcncelle":"Kaydet"]})]})]})})})})]})}},1395:(e,t,a)=>{Promise.resolve().then(a.bind(a,1270))},1968:(e,t)=>{"use strict";function a(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return a}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3143:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3613:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3861:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},4777:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return n}});let r=a(687),s=a(1215),i=a(9294),l=a(1968);function n(e){let{moduleIds:t}=e,a=i.workAsyncStorage.getStore();if(void 0===a)return null;let n=[];if(a.reactLoadableManifest&&t){let e=a.reactLoadableManifest;for(let a of t){if(!e[a])continue;let t=e[a].files;n.push(...t)}}return 0===n.length?null:(0,r.jsx)(r.Fragment,{children:n.map(e=>{let t=a.assetPrefix+"/_next/"+(0,l.encodeURIPath)(e);return e.endsWith(".css")?(0,r.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,s.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},4963:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let r=a(687),s=a(3210),i=a(6780),l=a(4777);function n(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(n(()=>null)),loading:null,ssr:!0},d=function(e){let t={...o,...e},a=(0,s.lazy)(()=>t.loader().then(n)),d=t.loading;function c(e){let n=d?(0,r.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,o=!t.ssr||!!t.loading,c=o?s.Suspense:s.Fragment,u=t.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.PreloadChunks,{moduleIds:t.modules}),(0,r.jsx)(a,{...e})]}):(0,r.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(a,{...e})});return(0,r.jsx)(c,{...o?{fallback:n}:{},children:u})}return c.displayName="LoadableComponent",c}},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5371:(e,t,a)=>{Promise.resolve().then(a.bind(a,9006))},6085:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},6474:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},6780:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return s}});let r=a(1208);function s(e){let{reason:t,children:a}=e;throw Object.defineProperty(new r.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},7992:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8233:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8730:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},8819:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9006:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\metaanalizgorup_website\\\\app\\\\control-area\\\\dashboard\\\\news\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\news\\page.tsx","default")},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9270:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9587:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let r=a(4985)._(a(4963));function s(e,t){var a;let s={};"function"==typeof e&&(s.loader=e);let i={...s,...t};return(0,r.default)({...i,modules:null==(a=i.loadableGenerated)?void 0:a.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,947,722,628],()=>a(674));module.exports=r})();