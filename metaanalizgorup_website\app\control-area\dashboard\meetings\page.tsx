'use client';

import { useState, useEffect } from 'react';
import {
  Calendar,
  User,
  Mail,
  Phone,
  Clock,
  MapPin,
  CheckCircle,
  AlertCircle,
  XCircle,
  Trash2,
  Check
} from 'lucide-react';

interface Meeting {
  id: number;
  name: string;
  email: string;
  phone: string;
  company: string;
  service?: string;
  meeting_type: string;
  preferred_date: string;
  preferred_time: string;
  location_preference: string;
  message: string;
  status: 'pending' | 'confirmed' | 'completed' | 'cancelled';
  created_at: string;
  site_id?: number;
  site_name?: string;
  site_code?: string;
  source?: string; // Kaynak bilgisi
}

export default function MeetingsPage() {
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDark, setIsDark] = useState(false);
  const [activeTab, setActiveTab] = useState<'unread'>('unread');
  const [showHeaderCard, setShowHeaderCard] = useState(true);
  const [sourceFilter, setSourceFilter] = useState<string>('all'); // Kaynak filtresi

  useEffect(() => {
    fetchMeetings();

    // Check theme
    const savedTheme = localStorage.getItem('admin-theme');
    if (savedTheme === 'dark') {
      setIsDark(true);
    }

    // Check if header card was dismissed in last 24 hours
    const dismissedTime = localStorage.getItem('meetings-header-dismissed');
    if (dismissedTime) {
      const dismissedDate = new Date(dismissedTime);
      const now = new Date();
      const hoursDiff = (now.getTime() - dismissedDate.getTime()) / (1000 * 60 * 60);

      if (hoursDiff < 24) {
        setShowHeaderCard(false);
      }
    }

    // Listen for theme changes
    const handleThemeChange = (event: CustomEvent) => {
      setIsDark(event.detail.isDark);
    };

    window.addEventListener('themeChange', handleThemeChange as EventListener);

    return () => {
      window.removeEventListener('themeChange', handleThemeChange as EventListener);
    };
  }, []);

  const fetchMeetings = async () => {
    try {
      const response = await fetch('/api/admin/meetings.php', {
        credentials: 'include'
      });
      if (response.ok) {
        const data = await response.json();
        setMeetings(data.data?.meetings || []);
      }
    } catch (error) {
      console.error('Error fetching meetings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const markAsRead = async (meetingId: number) => {
    try {
      const response = await fetch('/api/admin/meetings.php', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          id: meetingId,
          action: 'mark_read'
        }),
      });

      if (response.ok) {
        setMeetings(prev =>
          prev.map(meeting =>
            meeting.id === meetingId
              ? { ...meeting, status: 'confirmed' as const }
              : meeting
          )
        );
      }
    } catch (error) {
      console.error('Error marking meeting as read:', error);
    }
  };

  const getSiteBadge = (siteCode: string) => {
    switch (siteCode) {
      case 'metaanaliz-group':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            Meta Analiz Group
          </span>
        );
      case 'metaanaliz-musavirlik':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Meta Analiz Müşavirlik
          </span>
        );
      case 'mgsam':
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
            MGSAM
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            Bilinmeyen Site
          </span>
        );
    }
  };

  // Kaynak badge rengi
  const getSourceBadge = (source: string) => {
    switch (source) {
      case 'mgsam':
        return 'bg-orange-100 text-orange-800 border border-orange-200';
      case 'metaanalizgroup':
        return 'bg-blue-100 text-blue-800 border border-blue-200';
      case 'metaanaliz-musavirlik':
        return 'bg-green-100 text-green-800 border border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border border-gray-200';
    }
  };

  // Kaynak adı
  const getSourceName = (source: string) => {
    switch (source) {
      case 'mgsam':
        return 'MGSAM';
      case 'metaanalizgroup':
        return 'Meta Analiz Group';
      case 'metaanaliz-musavirlik':
        return 'Meta Analiz Müşavirlik';
      default:
        return source?.toUpperCase() || 'Bilinmeyen';
    }
  };

  const deleteMeeting = async (meetingId: number) => {
    if (!confirm('Bu toplantı talebini silmek istediğinizden emin misiniz?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/meetings.php?id=${meetingId}`, {
        method: 'DELETE',
        credentials: 'include'
      });

      if (response.ok) {
        setMeetings(prev => prev.filter(meeting => meeting.id !== meetingId));
      }
    } catch (error) {
      console.error('Error deleting meeting:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'confirmed':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-emerald-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor';
      case 'confirmed':
        return 'Onaylandı';
      case 'completed':
        return 'Tamamlandı';
      case 'cancelled':
        return 'İptal Edildi';
      default:
        return 'Bilinmiyor';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return isDark ? 'bg-amber-900/20 border-amber-800' : 'bg-amber-50 border-amber-200';
      case 'confirmed':
        return isDark ? 'bg-blue-900/20 border-blue-800' : 'bg-blue-50 border-blue-200';
      case 'completed':
        return isDark ? 'bg-emerald-900/20 border-emerald-800' : 'bg-emerald-50 border-emerald-200';
      case 'cancelled':
        return isDark ? 'bg-red-900/20 border-red-800' : 'bg-red-50 border-red-200';
      default:
        return isDark ? 'bg-gray-700/50 border-gray-600' : 'bg-gray-50 border-gray-200';
    }
  };

  const filteredMeetings = meetings.filter(meeting => {
    // Tüm toplantıları göster - filtreleme kaldırıldı
    return true;
  });

  const unreadCount = meetings.filter(meeting => meeting.status === 'pending').length;



  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className={`animate-spin rounded-full h-12 w-12 border-b-2 ${
          isDark ? 'border-blue-400' : 'border-blue-600'
        }`}></div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDark ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      <div className="p-6">
        {/* Tabs and Filters */}
        {/* Başlık */}
        <div className="mb-6">
          <h1 className={`text-2xl font-bold ${
            isDark ? 'text-white' : 'text-gray-900'
          }`}>
            Toplantı Talepleri
          </h1>
          <p className={`mt-2 ${
            isDark ? 'text-gray-400' : 'text-gray-600'
          }`}>
            Tüm sitelerden gelen toplantı taleplerini görüntüleyin ve yönetin.
          </p>
        </div>

        {/* Meetings List */}
      <div className={`rounded-2xl shadow-lg border ${
        isDark 
          ? 'bg-gray-800 border-gray-700' 
          : 'bg-white border-gray-200'
      }`}>
        <div className="p-6">
          <h2 className={`text-lg font-semibold mb-4 ${
            isDark ? 'text-white' : 'text-gray-900'
          }`}>
            Toplantılar ({meetings.length})
          </h2>

          {filteredMeetings.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className={`mx-auto h-12 w-12 mb-4 ${
                isDark ? 'text-gray-600' : 'text-gray-400'
              }`} />
              <p className={`text-lg font-medium ${
                isDark ? 'text-gray-300' : 'text-gray-600'
              }`}>
                {activeTab === 'unread' ? 'Bekleyen toplantı talebi bulunmuyor' : 'Henüz toplantı talebi bulunmuyor'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredMeetings.map((meeting) => (
                <div
                  key={meeting.id}
                  className={`p-4 rounded-xl border transition-all duration-200 hover:shadow-md ${
                    getStatusColor(meeting.status)
                  }`}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <h3 className={`font-semibold ${
                        isDark ? 'text-white' : 'text-gray-900'
                      }`}>
                        {meeting.name}
                      </h3>
                      <div className="flex items-center space-x-2">
                        {/* Source Badge - Sadece bir tane badge göster */}
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSourceBadge(meeting.source || 'metaanalizgroup')}`}>
                          {getSourceName(meeting.source || 'metaanalizgroup')}
                        </span>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(meeting.status)}
                          <span className={`text-xs font-medium ${
                            isDark ? 'text-gray-400' : 'text-gray-600'
                          }`}>
                            {getStatusText(meeting.status)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <span className={`text-xs ${
                      isDark ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      {new Date(meeting.created_at).toLocaleDateString('tr-TR')}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm">
                        <Mail className={`h-4 w-4 ${
                          isDark ? 'text-gray-400' : 'text-gray-500'
                        }`} />
                        <span className={isDark ? 'text-gray-300' : 'text-gray-600'}>
                          {meeting.email}
                        </span>
                      </div>
                      {meeting.phone && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Phone className={`h-4 w-4 ${
                            isDark ? 'text-gray-400' : 'text-gray-500'
                          }`} />
                          <span className={isDark ? 'text-gray-300' : 'text-gray-600'}>
                            {meeting.phone}
                          </span>
                        </div>
                      )}
                      {meeting.company && (
                        <div className="flex items-center space-x-2 text-sm">
                          <User className={`h-4 w-4 ${
                            isDark ? 'text-gray-400' : 'text-gray-500'
                          }`} />
                          <span className={isDark ? 'text-gray-300' : 'text-gray-600'}>
                            {meeting.company}
                          </span>
                        </div>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm">
                        <Calendar className={`h-4 w-4 ${
                          isDark ? 'text-gray-400' : 'text-gray-500'
                        }`} />
                        <span className={isDark ? 'text-gray-300' : 'text-gray-600'}>
                          {new Date(meeting.preferred_date).toLocaleDateString('tr-TR')}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        <Clock className={`h-4 w-4 ${
                          isDark ? 'text-gray-400' : 'text-gray-500'
                        }`} />
                        <span className={isDark ? 'text-gray-300' : 'text-gray-600'}>
                          {meeting.preferred_time}
                        </span>
                      </div>
                      {meeting.location_preference && (
                        <div className="flex items-center space-x-2 text-sm">
                          <MapPin className={`h-4 w-4 ${
                            isDark ? 'text-gray-400' : 'text-gray-500'
                          }`} />
                          <span className={isDark ? 'text-gray-300' : 'text-gray-600'}>
                            {meeting.location_preference}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {meeting.meeting_type && (
                    <div className="mb-3">
                      <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                        isDark 
                          ? 'bg-gray-700 text-gray-300' 
                          : 'bg-gray-100 text-gray-700'
                      }`}>
                        {meeting.meeting_type}
                      </span>
                    </div>
                  )}
                  
                  {meeting.message && (
                    <p className={`text-sm ${
                      isDark ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      {meeting.message}
                    </p>
                  )}

                  {/* Action Buttons */}
                  <div className="flex items-center justify-between mt-4">
                    <div></div>
                    <div className="flex items-center space-x-2">
                      {meeting.status === 'pending' && (
                        <button
                          onClick={() => markAsRead(meeting.id)}
                          className={`p-2 rounded-lg transition-colors duration-200 ${
                            isDark
                              ? 'text-blue-400 hover:bg-blue-900/20 hover:text-blue-300'
                              : 'text-blue-600 hover:bg-blue-50 hover:text-blue-700'
                          }`}
                          title="Onayla"
                        >
                          <Check className="h-4 w-4" />
                        </button>
                      )}

                      <button
                        onClick={() => deleteMeeting(meeting.id)}
                        className={`p-2 rounded-lg transition-colors duration-200 ${
                          isDark
                            ? 'text-red-400 hover:bg-red-900/20 hover:text-red-300'
                            : 'text-red-600 hover:bg-red-50 hover:text-red-700'
                        }`}
                        title="Toplantı talebini sil"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        </div>
      </div>
    </div>
  );
}
