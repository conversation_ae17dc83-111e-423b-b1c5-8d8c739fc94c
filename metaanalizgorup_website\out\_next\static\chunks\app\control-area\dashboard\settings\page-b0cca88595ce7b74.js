(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[887],{211:(e,r,t)=>{Promise.resolve().then(t.bind(t,2550))},2550:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var a=t(5155),s=t(2115),l=t(9946);let i=(0,l.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),d=(0,l.A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);var n=t(3786),o=t(4229);let c=(0,l.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var x=t(3717);let m=(0,l.A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),g=(0,l.A)("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]);function h(){let[e,r]=(0,s.useState)(!1),[t,l]=(0,s.useState)("sites"),[h,y]=(0,s.useState)([]),[u,p]=(0,s.useState)(!0),[b,v]=(0,s.useState)(null),[j,f]=(0,s.useState)({selectedModel:"gpt-4o",systemPrompt:"Sen yardımcı bir AI asistanısın. T\xfcrk\xe7e olarak profesyonel ve yararlı yanıtlar veriyorsun.",temperature:.7,maxTokens:2e3,openaiApiKey:"",claudeApiKey:"",geminiApiKey:""}),[N]=(0,s.useState)([{id:"gpt-4o",name:"GPT-4o",provider:"openai",description:"En gelişmiş GPT-4 modeli",isActive:!0},{id:"gpt-4-turbo",name:"GPT-4 Turbo",provider:"openai",description:"Hızlı ve g\xfc\xe7l\xfc GPT-4",isActive:!0},{id:"gpt-3.5-turbo",name:"GPT-3.5 Turbo",provider:"openai",description:"Hızlı ve ekonomik model",isActive:!0},{id:"claude-3-5-sonnet",name:"Claude 3.5 Sonnet",provider:"claude",description:"En gelişmiş Claude modeli",isActive:!0},{id:"claude-3-opus",name:"Claude 3 Opus",provider:"claude",description:"G\xfc\xe7l\xfc analitik yetenekler",isActive:!0},{id:"claude-3-haiku",name:"Claude 3 Haiku",provider:"claude",description:"Hızlı ve verimli model",isActive:!0},{id:"gemini-1.5-pro",name:"Gemini 1.5 Pro",provider:"gemini",description:"Google'ın en gelişmiş modeli",isActive:!0},{id:"gemini-1.5-flash",name:"Gemini 1.5 Flash",provider:"gemini",description:"Hızlı ve verimli Gemini",isActive:!0},{id:"gemini-1.0-pro",name:"Gemini 1.0 Pro",provider:"gemini",description:"G\xfcvenilir Gemini modeli",isActive:!0}]);(0,s.useEffect)(()=>{w(),"dark"===localStorage.getItem("admin-theme")&&r(!0);let e=e=>{r(e.detail.isDark)};return window.addEventListener("themeChange",e),()=>{window.removeEventListener("themeChange",e)}},[]);let w=async()=>{try{let e=await fetch("/api/admin/sites.php"),r=await e.json();r.success&&y(r.data.sites)}catch(e){}finally{p(!1)}},k=async e=>{try{let r=await fetch("/api/admin/sites.php",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),t=await r.json();t.success?(w(),v(null)):alert("Hata: "+t.message)}catch(e){alert("G\xfcncelleme sırasında hata oluştu.")}},A=e=>{switch(e){case"metaanaliz-group":return(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:"Meta Analiz Group"});case"metaanaliz-musavirlik":return(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Meta Analiz M\xfcşavirlik"});default:return(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:e})}};return u?(0,a.jsx)("div",{className:`min-h-screen flex items-center justify-center ${e?"bg-gray-900":"bg-gray-50"}`,children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"})}):(0,a.jsx)("div",{className:`min-h-screen transition-colors duration-300 ${e?"bg-gray-900":"bg-gray-50"}`,children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:`text-3xl font-bold ${e?"text-white":"text-gray-900"}`,children:"Ayarlar"}),(0,a.jsx)("p",{className:`mt-2 text-sm ${e?"text-gray-400":"text-gray-600"}`,children:"Sistemdeki siteleri y\xf6netin ve AI ayarlarını yapılandırın"})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:`border-b ${e?"border-gray-700":"border-gray-200"}`,children:(0,a.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,a.jsx)("button",{onClick:()=>l("sites"),className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${"sites"===t?e?"border-blue-400 text-blue-400":"border-blue-500 text-blue-600":e?"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(i,{className:"w-4 h-4 mr-2"}),"Site Y\xf6netimi"]})}),(0,a.jsx)("button",{onClick:()=>l("ai"),className:`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${"ai"===t?e?"border-blue-400 text-blue-400":"border-blue-500 text-blue-600":e?"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(d,{className:"w-4 h-4 mr-2"}),"AI Ayarları"]})})]})})}),"sites"===t&&(0,a.jsx)("div",{className:`rounded-2xl shadow-lg border transition-all duration-300 ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:`${e?"bg-gray-700":"bg-gray-50"}`,children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:`px-6 py-4 text-left text-sm font-semibold ${e?"text-gray-200":"text-gray-900"}`,children:"Site"}),(0,a.jsx)("th",{className:`px-6 py-4 text-left text-sm font-semibold ${e?"text-gray-200":"text-gray-900"}`,children:"URL"}),(0,a.jsx)("th",{className:`px-6 py-4 text-left text-sm font-semibold ${e?"text-gray-200":"text-gray-900"}`,children:"A\xe7ıklama"}),(0,a.jsx)("th",{className:`px-6 py-4 text-left text-sm font-semibold ${e?"text-gray-200":"text-gray-900"}`,children:"Durum"}),(0,a.jsx)("th",{className:`px-6 py-4 text-left text-sm font-semibold ${e?"text-gray-200":"text-gray-900"}`,children:"İşlemler"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:h.map(r=>(0,a.jsxs)("tr",{className:`${e?"hover:bg-gray-700":"hover:bg-gray-50"} transition-colors duration-200`,children:[(0,a.jsx)("td",{className:`px-6 py-4 text-sm ${e?"text-gray-300":"text-gray-900"}`,children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(i,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:r.site_name}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:A(r.site_code)})]})]})}),(0,a.jsx)("td",{className:`px-6 py-4 text-sm ${e?"text-gray-300":"text-gray-900"}`,children:(null==b?void 0:b.id)===r.id?(0,a.jsx)("input",{type:"url",value:b.site_url,onChange:e=>v({...b,site_url:e.target.value}),className:`w-full p-2 rounded border ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`}):(0,a.jsxs)("a",{href:r.site_url,target:"_blank",rel:"noopener noreferrer",className:"flex items-center space-x-1 text-blue-600 hover:text-blue-800",children:[(0,a.jsx)("span",{children:r.site_url}),(0,a.jsx)(n.A,{className:"h-3 w-3"})]})}),(0,a.jsx)("td",{className:`px-6 py-4 text-sm ${e?"text-gray-300":"text-gray-900"}`,children:(null==b?void 0:b.id)===r.id?(0,a.jsx)("textarea",{value:b.description,onChange:e=>v({...b,description:e.target.value}),className:`w-full p-2 rounded border ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`,rows:2}):(0,a.jsx)("div",{className:"max-w-xs truncate",title:r.description,children:r.description})}),(0,a.jsx)("td",{className:`px-6 py-4 text-sm ${e?"text-gray-300":"text-gray-900"}`,children:(null==b?void 0:b.id)===r.id?(0,a.jsxs)("select",{value:b.is_active?"1":"0",onChange:e=>v({...b,is_active:"1"===e.target.value}),className:`p-2 rounded border ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`,children:[(0,a.jsx)("option",{value:"1",children:"Aktif"}),(0,a.jsx)("option",{value:"0",children:"Pasif"})]}):(0,a.jsx)("span",{className:`px-2 py-1 rounded text-xs font-medium ${r.is_active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:r.is_active?"Aktif":"Pasif"})}),(0,a.jsx)("td",{className:"px-6 py-4 text-sm",children:(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(null==b?void 0:b.id)===r.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>k(b),className:`p-2 rounded-lg transition-colors duration-200 ${e?"text-green-400 hover:bg-green-900/20 hover:text-green-300":"text-green-600 hover:bg-green-50 hover:text-green-700"}`,title:"Kaydet",children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>v(null),className:`p-2 rounded-lg transition-colors duration-200 ${e?"text-red-400 hover:bg-red-900/20 hover:text-red-300":"text-red-600 hover:bg-red-50 hover:text-red-700"}`,title:"İptal",children:(0,a.jsx)(c,{className:"h-4 w-4"})})]}):(0,a.jsx)("button",{onClick:()=>v(r),className:`p-2 rounded-lg transition-colors duration-200 ${e?"text-blue-400 hover:bg-blue-900/20 hover:text-blue-300":"text-blue-600 hover:bg-blue-50 hover:text-blue-700"}`,title:"D\xfczenle",children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})})})]},r.id))})]})})}),"ai"===t&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:`rounded-2xl shadow-lg border transition-all duration-300 ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[(0,a.jsxs)("div",{className:`px-6 py-4 border-b ${e?"border-gray-700":"border-gray-200"}`,children:[(0,a.jsx)("h3",{className:`text-lg font-semibold ${e?"text-white":"text-gray-900"}`,children:"AI Model Se\xe7imi"}),(0,a.jsx)("p",{className:`mt-1 text-sm ${e?"text-gray-400":"text-gray-600"}`,children:"Kullanılacak AI modelini se\xe7in"})]}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:`text-sm font-medium mb-3 flex items-center ${e?"text-gray-300":"text-gray-700"}`,children:[(0,a.jsx)(m,{className:"w-4 h-4 mr-2"}),"OpenAI"]}),(0,a.jsx)("div",{className:"space-y-2",children:N.filter(e=>"openai"===e.provider).map(r=>(0,a.jsxs)("label",{className:`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${j.selectedModel===r.id?e?"border-blue-500 bg-blue-500/10":"border-blue-500 bg-blue-50":e?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300"}`,children:[(0,a.jsx)("input",{type:"radio",name:"aiModel",value:r.id,checked:j.selectedModel===r.id,onChange:e=>f(r=>({...r,selectedModel:e.target.value})),className:"sr-only"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:`text-sm font-medium ${e?"text-white":"text-gray-900"}`,children:r.name}),(0,a.jsx)("div",{className:`text-xs ${e?"text-gray-400":"text-gray-500"}`,children:r.description})]}),j.selectedModel===r.id&&(0,a.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})]},r.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:`text-sm font-medium mb-3 flex items-center ${e?"text-gray-300":"text-gray-700"}`,children:[(0,a.jsx)(g,{className:"w-4 h-4 mr-2"}),"Claude"]}),(0,a.jsx)("div",{className:"space-y-2",children:N.filter(e=>"claude"===e.provider).map(r=>(0,a.jsxs)("label",{className:`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${j.selectedModel===r.id?e?"border-blue-500 bg-blue-500/10":"border-blue-500 bg-blue-50":e?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300"}`,children:[(0,a.jsx)("input",{type:"radio",name:"aiModel",value:r.id,checked:j.selectedModel===r.id,onChange:e=>f(r=>({...r,selectedModel:e.target.value})),className:"sr-only"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:`text-sm font-medium ${e?"text-white":"text-gray-900"}`,children:r.name}),(0,a.jsx)("div",{className:`text-xs ${e?"text-gray-400":"text-gray-500"}`,children:r.description})]}),j.selectedModel===r.id&&(0,a.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})]},r.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:`text-sm font-medium mb-3 flex items-center ${e?"text-gray-300":"text-gray-700"}`,children:[(0,a.jsx)(d,{className:"w-4 h-4 mr-2"}),"Gemini"]}),(0,a.jsx)("div",{className:"space-y-2",children:N.filter(e=>"gemini"===e.provider).map(r=>(0,a.jsxs)("label",{className:`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${j.selectedModel===r.id?e?"border-blue-500 bg-blue-500/10":"border-blue-500 bg-blue-50":e?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300"}`,children:[(0,a.jsx)("input",{type:"radio",name:"aiModel",value:r.id,checked:j.selectedModel===r.id,onChange:e=>f(r=>({...r,selectedModel:e.target.value})),className:"sr-only"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:`text-sm font-medium ${e?"text-white":"text-gray-900"}`,children:r.name}),(0,a.jsx)("div",{className:`text-xs ${e?"text-gray-400":"text-gray-500"}`,children:r.description})]}),j.selectedModel===r.id&&(0,a.jsx)("div",{className:"w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})})]},r.id))})]})]})})]}),(0,a.jsxs)("div",{className:`rounded-2xl shadow-lg border transition-all duration-300 ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[(0,a.jsxs)("div",{className:`px-6 py-4 border-b ${e?"border-gray-700":"border-gray-200"}`,children:[(0,a.jsx)("h3",{className:`text-lg font-semibold ${e?"text-white":"text-gray-900"}`,children:"API Anahtarları"}),(0,a.jsx)("p",{className:`mt-1 text-sm ${e?"text-gray-400":"text-gray-600"}`,children:"AI servislerine erişim i\xe7in API anahtarlarınızı girin"})]}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:`flex items-center text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:[(0,a.jsx)(m,{className:"w-4 h-4 mr-2"}),"OpenAI API Key"]}),(0,a.jsx)("input",{type:"password",value:j.openaiApiKey,onChange:e=>f(r=>({...r,openaiApiKey:e.target.value})),placeholder:"sk-...",className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${e?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:`flex items-center text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:[(0,a.jsx)(g,{className:"w-4 h-4 mr-2"}),"Claude API Key"]}),(0,a.jsx)("input",{type:"password",value:j.claudeApiKey,onChange:e=>f(r=>({...r,claudeApiKey:e.target.value})),placeholder:"sk-ant-...",className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${e?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:`flex items-center text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:[(0,a.jsx)(d,{className:"w-4 h-4 mr-2"}),"Gemini API Key"]}),(0,a.jsx)("input",{type:"password",value:j.geminiApiKey,onChange:e=>f(r=>({...r,geminiApiKey:e.target.value})),placeholder:"AIza...",className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${e?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`})]})]})})]}),(0,a.jsxs)("div",{className:`rounded-2xl shadow-lg border transition-all duration-300 ${e?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:[(0,a.jsxs)("div",{className:`px-6 py-4 border-b ${e?"border-gray-700":"border-gray-200"}`,children:[(0,a.jsx)("h3",{className:`text-lg font-semibold ${e?"text-white":"text-gray-900"}`,children:"System Prompt Ayarları"}),(0,a.jsx)("p",{className:`mt-1 text-sm ${e?"text-gray-400":"text-gray-600"}`,children:"AI'nın davranışını belirleyen sistem talimatları"})]}),(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:`block text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:"System Prompt"}),(0,a.jsx)("textarea",{value:j.systemPrompt,onChange:e=>f(r=>({...r,systemPrompt:e.target.value})),rows:6,className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${e?"bg-gray-700 border-gray-600 text-white placeholder-gray-400":"bg-white border-gray-300 text-gray-900 placeholder-gray-500"}`,placeholder:"AI'nın nasıl davranacağını belirleyen talimatları yazın..."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:`block text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:["Temperature (",j.temperature,")"]}),(0,a.jsx)("input",{type:"range",min:"0",max:"2",step:"0.1",value:j.temperature,onChange:e=>f(r=>({...r,temperature:parseFloat(e.target.value)})),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"}),(0,a.jsx)("div",{className:`text-xs mt-1 ${e?"text-gray-400":"text-gray-500"}`,children:"D\xfcş\xfck: Daha tutarlı, Y\xfcksek: Daha yaratıcı"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:`block text-sm font-medium mb-2 ${e?"text-gray-300":"text-gray-700"}`,children:"Max Tokens"}),(0,a.jsx)("input",{type:"number",min:"100",max:"4000",value:j.maxTokens,onChange:e=>f(r=>({...r,maxTokens:parseInt(e.target.value)})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors ${e?"bg-gray-700 border-gray-600 text-white":"bg-white border-gray-300 text-gray-900"}`}),(0,a.jsx)("div",{className:`text-xs mt-1 ${e?"text-gray-400":"text-gray-500"}`,children:"Maksimum yanıt uzunluğu"})]})]})]})})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)("button",{onClick:()=>{alert("AI ayarları kaydedildi!")},className:"px-6 py-2 rounded-lg font-medium transition-colors bg-blue-600 hover:bg-blue-700 text-white",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Ayarları Kaydet"]})})})]})]})})}},3717:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3786:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4229:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(9946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9946:(e,r,t)=>{"use strict";t.d(r,{A:()=>x});var a=t(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),i=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},d=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},n=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:c="",children:x,iconNode:m,...g}=e;return(0,a.createElement)("svg",{ref:r,...o,width:s,height:s,stroke:t,strokeWidth:i?24*Number(l)/Number(s):l,className:d("lucide",c),...!x&&!n(g)&&{"aria-hidden":"true"},...g},[...m.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(x)?x:[x]])}),x=(e,r)=>{let t=(0,a.forwardRef)((t,l)=>{let{className:n,...o}=t;return(0,a.createElement)(c,{ref:l,iconNode:r,className:d(`lucide-${s(i(e))}`,`lucide-${e}`,n),...o})});return t.displayName=i(e),t}}},e=>{var r=r=>e(e.s=r);e.O(0,[441,684,358],()=>r(211)),_N_E=e.O()}]);