(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[536],{646:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1007:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2852:(e,t,a)=>{Promise.resolve().then(a.bind(a,5163))},4186:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4516:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5163:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var r=a(5155),s=a(2115),l=a(4186),i=a(646);let n=(0,a(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var c=a(5339),d=a(9074),m=a(8883),x=a(9420),o=a(1007),h=a(4516),g=a(5196),u=a(2525);function y(){let[e,t]=(0,s.useState)([]),[a,y]=(0,s.useState)(!0),[p,b]=(0,s.useState)(!1),[f,v]=(0,s.useState)("unread"),[j,N]=(0,s.useState)(!0),[k,w]=(0,s.useState)("all");(0,s.useEffect)(()=>{A(),"dark"===localStorage.getItem("admin-theme")&&b(!0);let e=localStorage.getItem("meetings-header-dismissed");if(e){let t=new Date(e);(new Date().getTime()-t.getTime())/36e5<24&&N(!1)}let t=e=>{b(e.detail.isDark)};return window.addEventListener("themeChange",t),()=>{window.removeEventListener("themeChange",t)}},[]);let A=async()=>{try{let a=await fetch("/api/admin/meetings.php",{credentials:"include"});if(a.ok){var e;let r=await a.json();t((null==(e=r.data)?void 0:e.meetings)||[])}}catch(e){}finally{y(!1)}},$=async e=>{try{(await fetch("/api/admin/meetings.php",{method:"PATCH",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({id:e,action:"mark_read"})})).ok&&t(t=>t.map(t=>t.id===e?{...t,status:"confirmed"}:t))}catch(e){}},z=e=>{switch(e){case"mgsam":return"bg-orange-100 text-orange-800 border border-orange-200";case"metaanalizgroup":return"bg-blue-100 text-blue-800 border border-blue-200";case"metaanaliz-musavirlik":return"bg-green-100 text-green-800 border border-green-200";default:return"bg-gray-100 text-gray-800 border border-gray-200"}},C=e=>{switch(e){case"mgsam":return"MGSAM";case"metaanalizgroup":return"Meta Analiz Group";case"metaanaliz-musavirlik":return"Meta Analiz M\xfcşavirlik";default:return(null==e?void 0:e.toUpperCase())||"Bilinmeyen"}},M=async e=>{if(confirm("Bu toplantı talebini silmek istediğinizden emin misiniz?"))try{(await fetch(`/api/admin/meetings.php?id=${e}`,{method:"DELETE",credentials:"include"})).ok&&t(t=>t.filter(t=>t.id!==e))}catch(e){}},_=e=>{switch(e){case"pending":return(0,r.jsx)(l.A,{className:"h-4 w-4 text-amber-500"});case"confirmed":return(0,r.jsx)(i.A,{className:"h-4 w-4 text-blue-500"});case"completed":return(0,r.jsx)(i.A,{className:"h-4 w-4 text-emerald-500"});case"cancelled":return(0,r.jsx)(n,{className:"h-4 w-4 text-red-500"});default:return(0,r.jsx)(c.A,{className:"h-4 w-4 text-gray-500"})}},E=e=>{switch(e){case"pending":return"Bekliyor";case"confirmed":return"Onaylandı";case"completed":return"Tamamlandı";case"cancelled":return"İptal Edildi";default:return"Bilinmiyor"}},T=e=>{switch(e){case"pending":return p?"bg-amber-900/20 border-amber-800":"bg-amber-50 border-amber-200";case"confirmed":return p?"bg-blue-900/20 border-blue-800":"bg-blue-50 border-blue-200";case"completed":return p?"bg-emerald-900/20 border-emerald-800":"bg-emerald-50 border-emerald-200";case"cancelled":return p?"bg-red-900/20 border-red-800":"bg-red-50 border-red-200";default:return p?"bg-gray-700/50 border-gray-600":"bg-gray-50 border-gray-200"}},S=e.filter(e=>!0);return(e.filter(e=>"pending"===e.status).length,a)?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:`animate-spin rounded-full h-12 w-12 border-b-2 ${p?"border-blue-400":"border-blue-600"}`})}):(0,r.jsx)("div",{className:`min-h-screen transition-colors duration-300 ${p?"bg-gray-900":"bg-gray-50"}`,children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:`text-2xl font-bold ${p?"text-white":"text-gray-900"}`,children:"Toplantı Talepleri"}),(0,r.jsx)("p",{className:`mt-2 ${p?"text-gray-400":"text-gray-600"}`,children:"T\xfcm sitelerden gelen toplantı taleplerini g\xf6r\xfcnt\xfcleyin ve y\xf6netin."})]}),(0,r.jsx)("div",{className:`rounded-2xl shadow-lg border ${p?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("h2",{className:`text-lg font-semibold mb-4 ${p?"text-white":"text-gray-900"}`,children:["Toplantılar (",e.length,")"]}),0===S.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(d.A,{className:`mx-auto h-12 w-12 mb-4 ${p?"text-gray-600":"text-gray-400"}`}),(0,r.jsx)("p",{className:`text-lg font-medium ${p?"text-gray-300":"text-gray-600"}`,children:"unread"===f?"Bekleyen toplantı talebi bulunmuyor":"Hen\xfcz toplantı talebi bulunmuyor"})]}):(0,r.jsx)("div",{className:"space-y-4",children:S.map(e=>(0,r.jsxs)("div",{className:`p-4 rounded-xl border transition-all duration-200 hover:shadow-md ${T(e.status)}`,children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("h3",{className:`font-semibold ${p?"text-white":"text-gray-900"}`,children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${z(e.source||"metaanalizgroup")}`,children:C(e.source||"metaanalizgroup")}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[_(e.status),(0,r.jsx)("span",{className:`text-xs font-medium ${p?"text-gray-400":"text-gray-600"}`,children:E(e.status)})]})]})]}),(0,r.jsx)("span",{className:`text-xs ${p?"text-gray-400":"text-gray-500"}`,children:new Date(e.created_at).toLocaleDateString("tr-TR")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(m.A,{className:`h-4 w-4 ${p?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:p?"text-gray-300":"text-gray-600",children:e.email})]}),e.phone&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(x.A,{className:`h-4 w-4 ${p?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:p?"text-gray-300":"text-gray-600",children:e.phone})]}),e.company&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(o.A,{className:`h-4 w-4 ${p?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:p?"text-gray-300":"text-gray-600",children:e.company})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(d.A,{className:`h-4 w-4 ${p?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:p?"text-gray-300":"text-gray-600",children:new Date(e.preferred_date).toLocaleDateString("tr-TR")})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(l.A,{className:`h-4 w-4 ${p?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:p?"text-gray-300":"text-gray-600",children:e.preferred_time})]}),e.location_preference&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(h.A,{className:`h-4 w-4 ${p?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:p?"text-gray-300":"text-gray-600",children:e.location_preference})]})]})]}),e.meeting_type&&(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsx)("span",{className:`inline-block px-3 py-1 rounded-full text-xs font-medium ${p?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-700"}`,children:e.meeting_type})}),e.message&&(0,r.jsx)("p",{className:`text-sm ${p?"text-gray-400":"text-gray-600"}`,children:e.message}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,r.jsx)("div",{}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:["pending"===e.status&&(0,r.jsx)("button",{onClick:()=>$(e.id),className:`p-2 rounded-lg transition-colors duration-200 ${p?"text-blue-400 hover:bg-blue-900/20 hover:text-blue-300":"text-blue-600 hover:bg-blue-50 hover:text-blue-700"}`,title:"Onayla",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>M(e.id),className:`p-2 rounded-lg transition-colors duration-200 ${p?"text-red-400 hover:bg-red-900/20 hover:text-red-300":"text-red-600 hover:bg-red-50 hover:text-red-700"}`,title:"Toplantı talebini sil",children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})]})]})]},e.id))})]})})]})})}},5196:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5339:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8883:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9074:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9420:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9946:(e,t,a)=>{"use strict";a.d(t,{A:()=>x});var r=a(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let m=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:m="",children:x,iconNode:o,...h}=e;return(0,r.createElement)("svg",{ref:t,...d,width:s,height:s,stroke:a,strokeWidth:i?24*Number(l)/Number(s):l,className:n("lucide",m),...!x&&!c(h)&&{"aria-hidden":"true"},...h},[...o.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(x)?x:[x]])}),x=(e,t)=>{let a=(0,r.forwardRef)((a,l)=>{let{className:c,...d}=a;return(0,r.createElement)(m,{ref:l,iconNode:t,className:n(`lucide-${s(i(e))}`,`lucide-${e}`,c),...d})});return a.displayName=i(e),a}}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(2852)),_N_E=e.O()}]);