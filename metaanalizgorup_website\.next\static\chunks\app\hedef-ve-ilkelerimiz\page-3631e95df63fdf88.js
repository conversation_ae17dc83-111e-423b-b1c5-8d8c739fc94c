(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[847],{3374:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>o});var r=l(5155),i=l(6874),s=l.n(i),a=l(7864),n=l(5922);function o(){let e=[{icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),title:"M\xfcşteri Odaklı M\xfckemmellik",description:"M\xfcşterilerimizin ihtiya\xe7larını en iyi şekilde anlayarak, onların beklentilerini aşan \xe7\xf6z\xfcmler sunmak ve s\xfcrd\xfcr\xfclebilir değer yaratmak."},{icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),title:"S\xfcrekli Gelişim ve İnovasyon",description:"Kendimizi ve hizmetlerimizi s\xfcrekli geliştirerek, sekt\xf6rde \xf6nc\xfc konumumuzu korumak ve yenilik\xe7i \xe7\xf6z\xfcmler sunmak."},{icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),title:"G\xfc\xe7l\xfc Ekip Ruhu",description:"Uzman kadromuzla g\xfc\xe7l\xfc ekip ruhu oluşturarak, birlikte başarıya ulaşmak ve s\xfcrd\xfcr\xfclebilir kalkınmaya katkı sağlamak."}],t=[{icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),title:"G\xfcvenilirlik",description:"T\xfcm iş s\xfcre\xe7lerimizde d\xfcr\xfcstl\xfck, şeffaflık ve etik değerler ilkesiyle hareket ederiz."},{icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})}),title:"Kalite",description:"Her projede en y\xfcksek kalite standartlarını benimser ve uygularız."},{icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 0 1 9-9"})}),title:"Yenilik\xe7ilik",description:"Teknolojik gelişmeleri takip ederek yenilik\xe7i \xe7\xf6z\xfcmler sunarız."},{icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})}),title:"Sorumluluk",description:"Sosyal ve \xe7evresel sorumluluklarımızı yerine getiririz."},{icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})}),title:"S\xfcrekli \xd6ğrenme",description:"S\xfcrekli \xf6ğrenme ve gelişim k\xfclt\xfcr\xfcn\xfc benimseriz."},{icon:(0,r.jsx)("svg",{className:"w-10 h-10",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),title:"Etik Değerler",description:"T\xfcm faaliyetlerimizde etik değerlere bağlı kalırız."}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-white",children:[(0,r.jsx)(a.A,{currentPage:"hedef-ve-ilkelerimiz"}),(0,r.jsxs)("section",{className:"pt-32 pb-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-slate-900/50 to-transparent"}),(0,r.jsx)("div",{className:"absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-slate-600/20 to-transparent"}),(0,r.jsx)("div",{className:"container mx-auto px-4 relative z-10",children:(0,r.jsxs)("div",{className:"max-w-5xl mx-auto text-center",children:[(0,r.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-slate-700/50 rounded-full text-slate-300 text-sm font-medium mb-8 backdrop-blur-sm border border-slate-600/30",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-gradient-to-r from-green-400 to-green-300 rounded-full mr-3"})," ","Kurumsal Vizyonumuz"]}),(0,r.jsxs)("h1",{className:"text-5xl md:text-7xl font-extralight text-white mb-8 tracking-tight",children:["Hedef ve"," ",(0,r.jsx)("span",{className:"bg-gradient-to-r from-slate-200 via-slate-100 to-slate-300 bg-clip-text text-transparent font-light",children:"İlkelerimiz"})]}),(0,r.jsx)("p",{className:"text-xl md:text-2xl text-slate-300 leading-relaxed max-w-3xl mx-auto font-light",children:"Doğru bilgi, iyi y\xf6netim ve s\xfcrd\xfcr\xfclebilir kalkınmanın temelini oluşturan değerlerimiz"})]})})]}),(0,r.jsx)("section",{className:"py-24 bg-gradient-to-br from-slate-50 to-white relative",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8",children:e.map((e,t)=>(0,r.jsxs)("div",{className:"group bg-white rounded-2xl p-3 md:p-6 lg:p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-slate-200/50 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute top-0 right-0 w-20 h-20 md:w-24 md:h-24 lg:w-32 lg:h-32 bg-gradient-to-br from-slate-100/50 to-transparent rounded-full -translate-y-10 translate-x-10 md:-translate-y-12 md:translate-x-12 lg:-translate-y-16 lg:translate-x-16"}),(0,r.jsx)("div",{className:"w-10 h-10 md:w-16 md:h-16 lg:w-20 lg:h-20 bg-gradient-to-br from-slate-700 to-slate-600 rounded-xl md:rounded-2xl flex items-center justify-center text-white mb-3 md:mb-6 lg:mb-8 group-hover:scale-110 transition-transform duration-300",children:(0,r.jsx)("div",{className:"w-6 h-6 md:w-10 md:h-10",children:e.icon})}),(0,r.jsx)("h3",{className:"text-base md:text-lg lg:text-2xl font-light text-slate-900 mb-2 md:mb-3 lg:mb-4 tracking-tight",children:e.title}),(0,r.jsx)("p",{className:"text-slate-600 leading-relaxed mb-3 md:mb-6 font-light text-sm md:text-base",children:e.description}),(0,r.jsx)("div",{className:"flex items-center text-slate-700 font-medium text-sm md:text-base",children:(0,r.jsx)("div",{className:"w-2 h-2 md:w-3 md:h-3 bg-gradient-to-r from-slate-500 to-slate-400 rounded-full mr-2 md:mr-3"})})]},`hedef-${e.title.slice(0,10)}-${t}`))})})})}),(0,r.jsxs)("section",{className:"py-24 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=%2260%22 height=%2260%22 viewBox=%220 0 60 60%22 xmlns=%22http://www.w3.org/2000/svg%22%3E%3Cg fill=%22none%22 fill-rule=%22evenodd%22%3E%3Cg fill=%22%23ffffff%22 fill-opacity=%220.03%22%3E%3Ccircle cx=%2230%22 cy=%2230%22 r=%224%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"}),(0,r.jsx)("div",{className:"container mx-auto px-4 relative z-10",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-20",children:[(0,r.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-slate-700/50 rounded-full text-slate-300 text-sm font-medium mb-8 backdrop-blur-sm border border-slate-600/30",children:[(0,r.jsx)("span",{className:"w-2 h-2 bg-gradient-to-r from-green-400 to-green-300 rounded-full mr-3"})," ","Temel İlkelerimiz"]}),(0,r.jsxs)("h2",{className:"text-4xl md:text-5xl font-extralight text-white mb-6 tracking-tight",children:["Değer Odaklı"," ",(0,r.jsx)("span",{className:"block text-slate-300 font-light",children:"İlkelerimiz"})]}),(0,r.jsx)("p",{className:"text-xl text-slate-300 max-w-3xl mx-auto font-light leading-relaxed",children:"\xc7alışmalarımızda rehber olan ve t\xfcm faaliyetlerimize y\xf6n veren temel değerlerimiz"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8",children:t.map((e,t)=>(0,r.jsxs)("div",{className:"group bg-slate-800/50 backdrop-blur-sm rounded-2xl p-3 md:p-6 lg:p-8 border border-slate-700/50 hover:border-slate-600/50 transition-all duration-500 transform hover:-translate-y-2 hover:bg-slate-800/70",children:[(0,r.jsx)("div",{className:"w-10 h-10 md:w-16 md:h-16 lg:w-20 lg:h-20 bg-gradient-to-br from-slate-600 to-slate-500 rounded-xl md:rounded-2xl flex items-center justify-center text-slate-200 mb-3 md:mb-6 lg:mb-8 group-hover:scale-110 transition-transform duration-300",children:(0,r.jsx)("div",{className:"w-6 h-6 md:w-10 md:h-10",children:e.icon})}),(0,r.jsx)("h3",{className:"text-base md:text-lg lg:text-xl font-light text-white mb-2 md:mb-3 lg:mb-4 tracking-tight",children:e.title}),(0,r.jsx)("p",{className:"text-slate-300 leading-relaxed font-light text-sm md:text-base",children:e.description})]},`ilke-${e.title.slice(0,10)}-${t}`))})]})})]}),(0,r.jsx)("section",{className:"py-20 bg-white",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-light text-slate-900 mb-6",children:"Geleceği Birlikte Şekillendirelim"}),(0,r.jsx)("p",{className:"text-xl text-slate-600 mb-8 font-light",children:"Değerlerimiz ve hedeflerimizle birlikte s\xfcrd\xfcr\xfclebilir başarıya ulaşalım"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("a",{href:"/iletisim",className:"px-8 py-4 bg-slate-900 text-white rounded-xl hover:bg-slate-800 transition-colors duration-300 font-medium text-center",children:"İletişime Ge\xe7in"}),(0,r.jsx)(s(),{href:"/toplanti-planla",className:"px-8 py-4 border border-slate-300 text-slate-700 rounded-xl hover:border-slate-400 hover:bg-slate-50 transition-colors duration-300 font-medium text-center",children:"Toplantı Planla"})]})]})})}),(0,r.jsx)(n.A,{})]})}},3661:(e,t,l)=>{Promise.resolve().then(l.bind(l,3374))}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,49,441,684,358],()=>t(3661)),_N_E=e.O()}]);