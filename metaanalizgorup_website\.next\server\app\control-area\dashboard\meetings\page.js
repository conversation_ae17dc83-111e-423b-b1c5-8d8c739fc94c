(()=>{var e={};e.id=536,e.ids=[536],e.modules={117:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>h});var r=a(687),s=a(3210),i=a(8730),l=a(5336);let n=(0,a(2688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);var d=a(3613),o=a(228),c=a(3931),m=a(8340),x=a(8869),p=a(7992),u=a(3964),g=a(8233);function h(){let[e,t]=(0,s.useState)([]),[a,h]=(0,s.useState)(!0),[b,y]=(0,s.useState)(!1),[v,f]=(0,s.useState)("unread"),[k,j]=(0,s.useState)(!0),[N,w]=(0,s.useState)("all"),A=async e=>{try{(await fetch("/api/admin/meetings.php",{method:"PATCH",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({id:e,action:"mark_read"})})).ok&&t(t=>t.map(t=>t.id===e?{...t,status:"confirmed"}:t))}catch(e){}},z=e=>{switch(e){case"mgsam":return"bg-orange-100 text-orange-800 border border-orange-200";case"metaanalizgroup":return"bg-blue-100 text-blue-800 border border-blue-200";case"metaanaliz-musavirlik":return"bg-green-100 text-green-800 border border-green-200";default:return"bg-gray-100 text-gray-800 border border-gray-200"}},_=e=>{switch(e){case"mgsam":return"MGSAM";case"metaanalizgroup":return"Meta Analiz Group";case"metaanaliz-musavirlik":return"Meta Analiz M\xfcşavirlik";default:return e?.toUpperCase()||"Bilinmeyen"}},M=async e=>{if(confirm("Bu toplantı talebini silmek istediğinizden emin misiniz?"))try{(await fetch(`/api/admin/meetings.php?id=${e}`,{method:"DELETE",credentials:"include"})).ok&&t(t=>t.filter(t=>t.id!==e))}catch(e){}},$=e=>{switch(e){case"pending":return(0,r.jsx)(i.A,{className:"h-4 w-4 text-amber-500"});case"confirmed":return(0,r.jsx)(l.A,{className:"h-4 w-4 text-blue-500"});case"completed":return(0,r.jsx)(l.A,{className:"h-4 w-4 text-emerald-500"});case"cancelled":return(0,r.jsx)(n,{className:"h-4 w-4 text-red-500"});default:return(0,r.jsx)(d.A,{className:"h-4 w-4 text-gray-500"})}},C=e=>{switch(e){case"pending":return"Bekliyor";case"confirmed":return"Onaylandı";case"completed":return"Tamamlandı";case"cancelled":return"İptal Edildi";default:return"Bilinmiyor"}},P=e=>{switch(e){case"pending":return b?"bg-amber-900/20 border-amber-800":"bg-amber-50 border-amber-200";case"confirmed":return b?"bg-blue-900/20 border-blue-800":"bg-blue-50 border-blue-200";case"completed":return b?"bg-emerald-900/20 border-emerald-800":"bg-emerald-50 border-emerald-200";case"cancelled":return b?"bg-red-900/20 border-red-800":"bg-red-50 border-red-200";default:return b?"bg-gray-700/50 border-gray-600":"bg-gray-50 border-gray-200"}},D=e.filter(e=>!0);return(e.filter(e=>"pending"===e.status).length,a)?(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:`animate-spin rounded-full h-12 w-12 border-b-2 ${b?"border-blue-400":"border-blue-600"}`})}):(0,r.jsx)("div",{className:`min-h-screen transition-colors duration-300 ${b?"bg-gray-900":"bg-gray-50"}`,children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:`text-2xl font-bold ${b?"text-white":"text-gray-900"}`,children:"Toplantı Talepleri"}),(0,r.jsx)("p",{className:`mt-2 ${b?"text-gray-400":"text-gray-600"}`,children:"T\xfcm sitelerden gelen toplantı taleplerini g\xf6r\xfcnt\xfcleyin ve y\xf6netin."})]}),(0,r.jsx)("div",{className:`rounded-2xl shadow-lg border ${b?"bg-gray-800 border-gray-700":"bg-white border-gray-200"}`,children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("h2",{className:`text-lg font-semibold mb-4 ${b?"text-white":"text-gray-900"}`,children:["Toplantılar (",e.length,")"]}),0===D.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(o.A,{className:`mx-auto h-12 w-12 mb-4 ${b?"text-gray-600":"text-gray-400"}`}),(0,r.jsx)("p",{className:`text-lg font-medium ${b?"text-gray-300":"text-gray-600"}`,children:"unread"===v?"Bekleyen toplantı talebi bulunmuyor":"Hen\xfcz toplantı talebi bulunmuyor"})]}):(0,r.jsx)("div",{className:"space-y-4",children:D.map(e=>(0,r.jsxs)("div",{className:`p-4 rounded-xl border transition-all duration-200 hover:shadow-md ${P(e.status)}`,children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("h3",{className:`font-semibold ${b?"text-white":"text-gray-900"}`,children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${z(e.source||"metaanalizgroup")}`,children:_(e.source||"metaanalizgroup")}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[$(e.status),(0,r.jsx)("span",{className:`text-xs font-medium ${b?"text-gray-400":"text-gray-600"}`,children:C(e.status)})]})]})]}),(0,r.jsx)("span",{className:`text-xs ${b?"text-gray-400":"text-gray-500"}`,children:new Date(e.created_at).toLocaleDateString("tr-TR")})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(c.A,{className:`h-4 w-4 ${b?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:b?"text-gray-300":"text-gray-600",children:e.email})]}),e.phone&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(m.A,{className:`h-4 w-4 ${b?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:b?"text-gray-300":"text-gray-600",children:e.phone})]}),e.company&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(x.A,{className:`h-4 w-4 ${b?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:b?"text-gray-300":"text-gray-600",children:e.company})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(o.A,{className:`h-4 w-4 ${b?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:b?"text-gray-300":"text-gray-600",children:new Date(e.preferred_date).toLocaleDateString("tr-TR")})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(i.A,{className:`h-4 w-4 ${b?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:b?"text-gray-300":"text-gray-600",children:e.preferred_time})]}),e.location_preference&&(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(p.A,{className:`h-4 w-4 ${b?"text-gray-400":"text-gray-500"}`}),(0,r.jsx)("span",{className:b?"text-gray-300":"text-gray-600",children:e.location_preference})]})]})]}),e.meeting_type&&(0,r.jsx)("div",{className:"mb-3",children:(0,r.jsx)("span",{className:`inline-block px-3 py-1 rounded-full text-xs font-medium ${b?"bg-gray-700 text-gray-300":"bg-gray-100 text-gray-700"}`,children:e.meeting_type})}),e.message&&(0,r.jsx)("p",{className:`text-sm ${b?"text-gray-400":"text-gray-600"}`,children:e.message}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,r.jsx)("div",{}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:["pending"===e.status&&(0,r.jsx)("button",{onClick:()=>A(e.id),className:`p-2 rounded-lg transition-colors duration-200 ${b?"text-blue-400 hover:bg-blue-900/20 hover:text-blue-300":"text-blue-600 hover:bg-blue-50 hover:text-blue-700"}`,title:"Onayla",children:(0,r.jsx)(u.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>M(e.id),className:`p-2 rounded-lg transition-colors duration-200 ${b?"text-red-400 hover:bg-red-900/20 hover:text-red-300":"text-red-600 hover:bg-red-50 hover:text-red-700"}`,title:"Toplantı talebini sil",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})]})]},e.id))})]})})]})})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3148:(e,t,a)=>{Promise.resolve().then(a.bind(a,117))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3613:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3873:e=>{"use strict";e.exports=require("path")},3931:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},3964:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5004:(e,t,a)=>{Promise.resolve().then(a.bind(a,9947))},5336:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7992:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},8233:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},8636:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=a(5239),s=a(8088),i=a(6076),l=a(893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);a.d(t,n);let d={children:["",{children:["control-area",{children:["dashboard",{children:["meetings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,9947)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\meetings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,9593)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,8014)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\layout.tsx"],error:[()=>Promise.resolve().then(a.bind(a,2608)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\error.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,9766)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\loading.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,6076)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,2366)),"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\meetings\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/control-area/dashboard/meetings/page",pathname:"/control-area/dashboard/meetings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8730:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(2688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9947:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Meta A\\\\metaanalizmusavirlik\\\\metaanalizgorup_website\\\\app\\\\control-area\\\\dashboard\\\\meetings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\Meta A\\metaanalizmusavirlik\\metaanalizgorup_website\\app\\control-area\\dashboard\\meetings\\page.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,947,722,628],()=>a(8636));module.exports=r})();