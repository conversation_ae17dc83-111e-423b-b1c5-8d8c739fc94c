(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[542],{1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2657:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},7868:(e,t,r)=>{Promise.resolve().then(r.bind(r,9314))},9314:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(5155),a=r(2115),l=r(5695),i=r(6766),n=r(1007),o=r(9946);let d=(0,o.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),c=(0,o.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var u=r(2657);function h(){let[e,t]=(0,a.useState)({username:"",password:""}),[r,o]=(0,a.useState)(!1),[h,m]=(0,a.useState)(!1),[p,f]=(0,a.useState)(""),x=(0,l.useRouter)(),w=async t=>{if(t.preventDefault(),h)return;m(!0),f("");let{username:r,password:s}=e;try{let e="localhost"!==window.location.hostname,t=window.location.hostname,a=e?`${window.location.protocol}//${t.replace("www.","")}/api/admin/login.php`:"/api/admin/login.php",l=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:r,password:s}),credentials:"include"});if(!l.ok)throw Error(`HTTP error! status: ${l.status}`);let i=await l.json();i.success?(localStorage.setItem("admin-user",JSON.stringify(i.data.user)),x.push("/control-area/dashboard")):f(i.message||"Giriş yapılırken bir hata oluştu")}catch(e){f("Bağlantı hatası. L\xfctfen tekrar deneyin.")}finally{m(!1)}},b=r=>{t({...e,[r.target.name]:r.target.value})};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)("div",{className:"text-center mb-8",children:(0,s.jsx)("div",{className:"flex justify-center mb-8",children:(0,s.jsx)(i.default,{src:"/meta_group_logo.webp",alt:"Meta Analiz Group",width:200,height:60,className:"brightness-0 invert object-contain filter drop-shadow-sm",priority:!0})})}),(0,s.jsx)("div",{className:"bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20",children:(0,s.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-slate-200 mb-2",children:"Kullanıcı Adı"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(n.A,{className:"h-5 w-5 text-slate-400"})}),(0,s.jsx)("input",{type:"text",id:"username",name:"username",value:e.username,onChange:b,required:!0,className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",placeholder:"Kullanıcı adınızı girin"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-slate-200 mb-2",children:"Şifre"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d,{className:"h-5 w-5 text-slate-400"})}),(0,s.jsx)("input",{type:r?"text":"password",id:"password",name:"password",value:e.password,onChange:b,required:!0,className:"w-full pl-10 pr-12 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",placeholder:"Şifrenizi girin"}),(0,s.jsx)("button",{type:"button",onClick:()=>o(!r),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-slate-400 hover:text-white transition-colors duration-200",children:r?(0,s.jsx)(c,{className:"h-5 w-5"}):(0,s.jsx)(u.A,{className:"h-5 w-5"})})]})]}),p&&(0,s.jsx)("div",{className:"bg-red-500/20 border border-red-500/50 rounded-lg p-3 text-red-200 text-sm",children:p}),(0,s.jsx)("button",{type:"submit",disabled:h,className:"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-medium py-3 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",children:h?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Giriş yapılıyor..."]}):"Giriş Yap"})]})}),(0,s.jsx)("div",{className:"text-center mt-8",children:(0,s.jsx)("p",{className:"text-slate-400 text-sm",children:"\xa9 2025 Meta Analiz Group. T\xfcm hakları saklıdır."})})]})})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:c="",children:u,iconNode:h,...m}=e;return(0,s.createElement)("svg",{ref:t,...d,width:a,height:a,stroke:r,strokeWidth:i?24*Number(l)/Number(a):l,className:n("lucide",c),...!u&&!o(m)&&{"aria-hidden":"true"},...m},[...h.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,s.forwardRef)((r,l)=>{let{className:o,...d}=r;return(0,s.createElement)(c,{ref:l,iconNode:t,className:n(`lucide-${a(i(e))}`,`lucide-${e}`,o),...d})});return r.displayName=i(e),r}}},e=>{var t=t=>e(e.s=t);e.O(0,[766,441,684,358],()=>t(7868)),_N_E=e.O()}]);