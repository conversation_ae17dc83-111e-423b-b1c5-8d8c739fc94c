{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:file((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+)/", "destination": "/:file", "internal": true, "missing": [{"type": "header", "key": "x-nextjs-data"}], "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\.\\w+))/$"}, {"source": "/:notfile((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+)", "destination": "/:notfile/", "internal": true, "statusCode": 308, "regex": "^(?:/((?!\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\.]+))$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/control-area", "regex": "^/control\\-area(?:/)?$", "routeKeys": {}, "namedRegex": "^/control\\-area(?:/)?$"}, {"page": "/control-area/dashboard", "regex": "^/control\\-area/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/control\\-area/dashboard(?:/)?$"}, {"page": "/control-area/dashboard/articles", "regex": "^/control\\-area/dashboard/articles(?:/)?$", "routeKeys": {}, "namedRegex": "^/control\\-area/dashboard/articles(?:/)?$"}, {"page": "/control-area/dashboard/callbacks", "regex": "^/control\\-area/dashboard/callbacks(?:/)?$", "routeKeys": {}, "namedRegex": "^/control\\-area/dashboard/callbacks(?:/)?$"}, {"page": "/control-area/dashboard/contacts", "regex": "^/control\\-area/dashboard/contacts(?:/)?$", "routeKeys": {}, "namedRegex": "^/control\\-area/dashboard/contacts(?:/)?$"}, {"page": "/control-area/dashboard/meetings", "regex": "^/control\\-area/dashboard/meetings(?:/)?$", "routeKeys": {}, "namedRegex": "^/control\\-area/dashboard/meetings(?:/)?$"}, {"page": "/control-area/dashboard/news", "regex": "^/control\\-area/dashboard/news(?:/)?$", "routeKeys": {}, "namedRegex": "^/control\\-area/dashboard/news(?:/)?$"}, {"page": "/control-area/dashboard/settings", "regex": "^/control\\-area/dashboard/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/control\\-area/dashboard/settings(?:/)?$"}, {"page": "/farkliliklarimiz", "regex": "^/farkliliklarimiz(?:/)?$", "routeKeys": {}, "namedRegex": "^/farkliliklarimiz(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/hedef-ve-il<PERSON><PERSON><PERSON>z", "regex": "^/hedef\\-ve\\-il<PERSON><PERSON><PERSON>z(?:/)?$", "routeKeys": {}, "namedRegex": "^/hedef\\-ve\\-il<PERSON><PERSON><PERSON>z(?:/)?$"}, {"page": "/iletisim", "regex": "^/iletisim(?:/)?$", "routeKeys": {}, "namedRegex": "^/iletisim(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/toplanti-planla", "regex": "^/toplanti\\-planla(?:/)?$", "routeKeys": {}, "namedRegex": "^/toplanti\\-planla(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}