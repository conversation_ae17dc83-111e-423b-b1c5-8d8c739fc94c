<?php
// CORS headers - daha kapsamlı
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Max-Age: 86400');
header('Content-Type: application/json; charset=utf-8');

// OPTIONS request için hızlı yanıt
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config.php';

// Sadece POST isteklerini kabul et
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Sadece POST istekleri kabul edilir');
}

// JSON verilerini al
$input = json_decode(file_get_contents('php://input'), true);

// Debug: Gelen verileri logla
error_log("Meeting form input: " . json_encode($input));

// Gerekli alanları kontrol et - frontend'den 'name' geliyor ama veritabanında 'full_name' var
$requiredFields = ['name', 'email', 'preferred_date'];
$missingFields = [];

foreach ($requiredFields as $field) {
    if (!isset($input[$field]) || empty(trim($input[$field]))) {
        $missingFields[] = $field;
    }
}

if (!empty($missingFields)) {
    sendResponse(false, 'Ad Soyad, E-posta ve Tercih Edilen Tarih alanları zorunludur.');
}

// Verileri temizle - frontend'den 'name' geliyor, veritabanına 'full_name' olarak kaydediyoruz
$name = sanitizeInput($input['name']);
$email = sanitizeInput($input['email']);
$phone = isset($input['phone']) ? sanitizeInput($input['phone']) : '';
$service = isset($input['service']) ? sanitizeInput($input['service']) : 'genel-danismanlik';
$preferredDate = sanitizeInput($input['preferred_date']);
$preferredTime = isset($input['preferred_time']) ? sanitizeInput($input['preferred_time']) : '10:00';
$company = isset($input['company']) ? sanitizeInput($input['company']) : '';
$notes = isset($input['notes']) ? sanitizeInput($input['notes']) : '';
$meetingType = isset($input['meeting_type']) ? sanitizeInput($input['meeting_type']) : 'office';

// E-posta doğrulama
if (!validateEmail($email)) {
    sendResponse(false, 'Geçersiz e-posta adresi');
}

// Telefon doğrulama (opsiyonel)
if (!empty($phone) && !validatePhone($phone)) {
    sendResponse(false, 'Geçersiz telefon numarası formatı');
}

// Tarih doğrulama
$dateObj = DateTime::createFromFormat('Y-m-d', $preferredDate);
if (!$dateObj || $dateObj->format('Y-m-d') !== $preferredDate) {
    sendResponse(false, 'Geçersiz tarih formatı (YYYY-MM-DD)');
}

// Geçmiş tarih kontrolü
if ($dateObj < new DateTime('today')) {
    sendResponse(false, 'Geçmiş bir tarih seçilemez');
}

// Saat doğrulama
if (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $preferredTime)) {
    sendResponse(false, 'Geçersiz saat formatı (HH:MM)');
}

// Çalışma saatleri kontrolü (09:00 - 18:00)
$timeObj = DateTime::createFromFormat('H:i', $preferredTime);
$startTime = DateTime::createFromFormat('H:i', '09:00');
$endTime = DateTime::createFromFormat('H:i', '18:00');

if ($timeObj < $startTime || $timeObj > $endTime) {
    sendResponse(false, 'Toplantı saati 09:00 - 18:00 arasında olmalıdır');
}

// Hizmet türü kontrolü
$validServices = [
    'insan-kaynaklari',
    'emeklilik',
    'kurumsal-danismanlik',
    'sgk-uyusmazliklari',
    'is-sagligi-guvenligi',
    'mali-musavirlik',
    'genel-danismanlik'
];

if (!in_array($service, $validServices)) {
    sendResponse(false, 'Geçersiz hizmet türü');
}

// Toplantı türü kontrolü - Türkçe değerler
$validMeetingTypes = ['Online', 'Ofiste', 'Telefon', 'Danışmanlık', 'Sunum', 'Analiz', 'Diğer'];

// Frontend'den gelen değerleri Türkçe karşılıklarına çevir
$meetingTypeMapping = [
    'online' => 'Online',
    'office' => 'Ofiste',
    'phone' => 'Telefon',
    'consultation' => 'Danışmanlık',
    'presentation' => 'Sunum',
    'analysis' => 'Analiz',
    'other' => 'Diğer'
];

// Eğer mapping'de varsa çevir, yoksa default Online yap
if (isset($meetingTypeMapping[$meetingType])) {
    $meetingType = $meetingTypeMapping[$meetingType];
} else {
    $meetingType = 'Online';
}

if (!in_array($meetingType, $validMeetingTypes)) {
    sendResponse(false, 'Geçersiz toplantı türü');
}

try {
    // Hub veritabanına bağlan
    $pdo = getDBConnection();

    // Veritabanına kaydet
    $stmt = $pdo->prepare("
        INSERT INTO meeting_requests (site_code, full_name, email, phone, company, meeting_type, preferred_date, preferred_time, description, created_at, status, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), 'pending', ?, ?)
    ");

    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';

    $result = $stmt->execute([
        'metaanaliz-musavirlik',
        $name,
        $email,
        $phone,
        $company,
        $meetingType,
        $preferredDate,
        $preferredTime,
        $notes,
        $clientIP,
        $userAgent
    ]);

    if (!$result) {
        sendResponse(false, 'Veriler kaydedilirken bir hata oluştu');
    }

    $meetingId = $pdo->lastInsertId();

    // Referans numarası oluştur
    $referenceId = 'MT' . str_pad($meetingId, 6, '0', STR_PAD_LEFT);

    // Meeting data array oluştur
    $meetingData = [
        'reference_id' => $referenceId,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
        'timestamp' => date('Y-m-d H:i:s')
    ];

    // Bildirim oluştur
    $notificationTitle = "Yeni Toplantı Talebi - " . SITE_NAME;
    $notificationMessage = "Yeni toplantı talebi: {$name} ({$email}) - {$preferredDate} {$preferredTime}";

    createNotification($pdo, SITE_CODE, 'meeting', $meetingId, $notificationTitle, $notificationMessage);

// Hizmet adlarını Türkçe'ye çevir
$serviceNames = [
    'insan-kaynaklari' => 'İnsan Kaynakları',
    'emeklilik' => 'Emeklilik Hizmetleri',
    'kurumsal-danismanlik' => 'Kurumsal Danışmanlık',
    'sgk-uyusmazliklari' => 'SGK Uyuşmazlıkları',
    'is-sagligi-guvenligi' => 'İş Sağlığı ve Güvenliği',
    'mali-musavirlik' => 'Mali Müşavirlik',
    'genel-danismanlik' => 'Genel Danışmanlık'
];

$meetingTypeNames = [
    'Online' => 'Online',
    'Ofiste' => 'Ofiste',
    'Telefon' => 'Telefon',
    'Danışmanlık' => 'Danışmanlık',
    'Sunum' => 'Sunum',
    'Analiz' => 'Analiz',
    'Diğer' => 'Diğer'
];

// E-posta gönder
$emailSubject = "Yeni Toplantı Talebi - " . $serviceNames[$service];
$emailBody = "
Yeni bir toplantı talebi alındı:

Ad Soyad: {$name}
E-posta: {$email}
Telefon: {$phone}
Şirket: {$company}
Hizmet: {$serviceNames[$service]}
Tercih Edilen Tarih: {$preferredDate}
Tercih Edilen Saat: {$preferredTime}
Toplantı Türü: {$meetingTypeNames[$meetingType]}

Notlar:
{$notes}

Referans ID: {$meetingData['reference_id']}

---
IP Adresi: {$meetingData['ip']}
Tarih: {$meetingData['timestamp']}
";

$headers = [
    'From: ' . FROM_EMAIL,
    'Reply-To: ' . $email,
    'Content-Type: text/plain; charset=UTF-8'
];

// E-posta göndermeyi dene
$emailSent = mail(TO_EMAIL, $emailSubject, $emailBody, implode("\r\n", $headers));

// Müşteriye onay e-postası gönder
$customerEmailSubject = "Toplantı Talebiniz Alındı - Meta Analiz Müşavirlik";
$customerEmailBody = "
Sayın {$name},

Toplantı talebiniz başarıyla alınmıştır.

Talep Detayları:
- Hizmet: {$serviceNames[$service]}
- Tercih Edilen Tarih: {$preferredDate}
- Tercih Edilen Saat: {$preferredTime}
- Toplantı Türü: {$meetingTypeNames[$meetingType]}
- Referans ID: {$meetingData['reference_id']}

En kısa sürede size dönüş yaparak toplantı detaylarını kesinleştireceğiz.

Teşekkürler,
Meta Analiz Müşavirlik Ekibi
";

$customerHeaders = [
    'From: ' . FROM_EMAIL,
    'Content-Type: text/plain; charset=UTF-8'
];

$customerEmailSent = mail($email, $customerEmailSubject, $customerEmailBody, implode("\r\n", $customerHeaders));

    // Log kaydı
    logRequest('meeting_request', [
        'id' => $meetingId,
        'name' => $name,
        'email' => $email,
        'phone' => $phone,
        'service' => $service,
        'meeting_type' => $meetingType,
        'preferred_date' => $preferredDate,
        'preferred_time' => $preferredTime,
        'site_code' => SITE_CODE
    ]);

    sendResponse(true, 'Toplantı talebiniz başarıyla gönderildi. En kısa sürede size dönüş yapacağız.', [
        'meeting_id' => $meetingId,
        'reference_number' => $referenceId
    ]);

} catch (Exception $e) {
    error_log("Meeting request error: " . $e->getMessage());
    sendResponse(false, 'Bir hata oluştu. Lütfen tekrar deneyin.');
}
?>
