(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{554:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(5155),l=s(6874),r=s.n(l),n=s(6766),i=s(7864),m=s(5922),o=s(2115);function d(){return(0,o.useEffect)(()=>{if(!document.querySelector('script[src="https://unpkg.com/@elevenlabs/convai-widget-embed"]')){let e=document.createElement("script");e.src="https://unpkg.com/@elevenlabs/convai-widget-embed",e.async=!0,e.type="text/javascript",e.id="elevenlabs-widget-script",e.onerror=()=>{},document.body.appendChild(e)}return()=>{}},[]),(0,a.jsx)("elevenlabs-convai",{"agent-id":"agent_01k0rks5xre4va2xqywg8qm1m9",className:"w-full h-full"})}function x(){return(0,o.useEffect)(()=>{let e=new IntersectionObserver(e=>{e.forEach(e=>{e.isIntersecting&&e.target.classList.add("visible")})},{threshold:.1});return document.querySelectorAll(".fade-up, .fade-in").forEach(t=>{e.observe(t)}),()=>e.disconnect()},[]),(0,o.useEffect)(()=>{let e=()=>{document.querySelectorAll("elevenlabs-convai").forEach(e=>{let t=e.shadowRoot;t&&t.querySelectorAll("*").forEach(e=>{e.textContent&&(e.textContent.includes("Powered by ElevenLabs")||e.textContent.includes("Conversational AI")||e.textContent.includes("ElevenLabs")||e.textContent.includes("Powered by"))&&(e.style.display="none",e.style.visibility="hidden",e.style.opacity="0",e.style.height="0",e.style.width="0",e.style.position="absolute",e.style.left="-9999px",e.style.top="-9999px")}),e.querySelectorAll("*").forEach(e=>{e.textContent&&(e.textContent.includes("Powered by ElevenLabs")||e.textContent.includes("Conversational AI")||e.textContent.includes("ElevenLabs")||e.textContent.includes("Powered by"))&&(e.style.display="none",e.style.visibility="hidden",e.style.opacity="0",e.style.height="0",e.style.width="0",e.style.position="absolute",e.style.left="-9999px",e.style.top="-9999px")})})};e(),setTimeout(e,50),setTimeout(e,200),setTimeout(e,500),setTimeout(e,1e3);let t=new MutationObserver(()=>{e(),setTimeout(e,50)});t.observe(document.body,{childList:!0,subtree:!0,characterData:!0,attributes:!0});let s=setInterval(e,300);return()=>{t.disconnect(),clearInterval(s)}},[]),(0,a.jsxs)("div",{children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:`
          .fade-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease-out;
          }
          .fade-up.visible {
            opacity: 1;
            transform: translateY(0);
          }
          .glass-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
          }
          .glass-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
          }
          @keyframes fadeUp {
            from {
              opacity: 0;
              transform: translateY(30px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}}),(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Organization",name:"Meta Analiz Group",description:"15 yılı aşkın deneyimimizle, kurumunuzun potansiyelini en \xfcst d\xfczeye \xe7ıkarmanıza yardımcı oluyoruz. M\xfcşavirlik, emlak, eğitim ve e-ticaret alanlarında uzman danışmanlık hizmetleri.",url:"https://metaanalizgroup.com",logo:"https://metaanalizgroup.com/meta_group_logo.webp",address:{"@type":"PostalAddress",streetAddress:"Yenicami Mah. \xd6zmen Sok. No: 24/A",addressLocality:"S\xf6ke",addressRegion:"Aydın",postalCode:"09270",addressCountry:"TR"},contactPoint:{"@type":"ContactPoint",telephone:"+90-542-797-05-00",contactType:"customer service",email:"<EMAIL>"},sameAs:["https://www.facebook.com/@metaanaliz","https://www.instagram.com/meta.analiz.haber/"],foundingDate:"2008",numberOfEmployees:"50+",areaServed:"Turkey"})}}),(0,a.jsx)(i.A,{currentPage:"home"}),(0,a.jsxs)("section",{className:"relative h-screen flex items-center overflow-hidden pt-24",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 z-0"}),(0,a.jsx)("div",{className:"absolute inset-0 z-5",children:(0,a.jsx)(n.default,{src:"/herobackground.webp",alt:"Meta Analiz Group Hero Background",fill:!0,className:"object-cover",priority:!0,quality:85,sizes:"100vw"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-black/60 to-transparent z-10"}),(0,a.jsx)("div",{className:"container mx-auto px-6 relative z-20",children:(0,a.jsxs)("div",{className:"flex justify-between items-center relative",children:[(0,a.jsxs)("div",{className:"flex-1 max-w-3xl",children:[(0,a.jsxs)("h1",{className:"text-5xl md:text-7xl font-light text-white mb-6 fade-up",children:["Doğru Bilgi, İyi Y\xf6netim",(0,a.jsx)("br",{}),"S\xfcrd\xfcr\xfclebilir Kalkınma"]}),(0,a.jsx)("p",{className:"text-xl text-white/90 max-w-2xl mb-8 fade-up",style:{transitionDelay:"0.2s"},children:"15 yılı aşkın deneyimimizle, kurumunuzun potansiyelini en \xfcst d\xfczeye \xe7ıkarmanıza yardımcı oluyoruz."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)(r(),{href:"/iletisim",className:"glass-btn inline-flex items-center px-8 py-3 text-lg font-medium text-white hover:scale-105 transition-all duration-300",children:[(0,a.jsx)("span",{children:"Danışmanlık Alın"}),(0,a.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]}),(0,a.jsxs)(r(),{href:"/toplanti-planla",className:"glass-btn inline-flex items-center px-8 py-3 text-lg font-medium text-white hover:scale-105 transition-all duration-300",children:[(0,a.jsx)("span",{children:"Toplantı Planla"}),(0,a.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})})]})]})]}),(0,a.jsx)("div",{className:"w-[350px] h-[500px]",children:(0,a.jsx)(d,{})})]})})]}),(0,a.jsx)("section",{className:"py-24 bg-gradient-to-br from-gray-50 to-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-slate-100 rounded-full text-slate-700 text-sm font-medium mb-6",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-gradient-to-r from-green-500 to-green-400 rounded-full mr-3"})," ","Grup Şirketlerimiz"]}),(0,a.jsxs)("h2",{className:"text-4xl md:text-5xl font-light mb-4",children:["Meta Analiz ",(0,a.jsxs)("span",{className:"text-primary",children:[" ","Ekosistemi"]})]}),(0,a.jsx)("p",{className:"text-gray-700 max-w-3xl mx-auto text-lg leading-relaxed",children:"Farklı sekt\xf6rlerde uzmanlaşmış grup şirketlerimizle kapsamlı \xe7\xf6z\xfcmler sunuyoruz"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-2 lg:grid-cols-5 gap-4 md:gap-6 lg:gap-8 max-w-[1400px] mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-5 lg:p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col min-h-[280px] md:min-h-[320px] lg:min-h-[360px]",children:[(0,a.jsx)("div",{className:"w-20 h-20 md:w-24 md:h-24 lg:w-32 lg:h-32 mb-3 md:mb-4 mx-auto flex items-center justify-center",children:(0,a.jsx)(n.default,{src:"/logos/logo1.webp",alt:"Meta Global Stratejiler",width:160,height:160,className:"object-contain w-full h-full",priority:!0,sizes:"(max-width: 768px) 80px, (max-width: 1024px) 96px, 128px"})}),(0,a.jsxs)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold mb-2 md:mb-3 text-gray-900 text-center leading-tight",children:["Meta Global",(0,a.jsx)("br",{}),"Stratejiler"]}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-5 flex-grow text-center text-xs md:text-sm leading-relaxed",children:"Ekonomik ve Siyasal Araştırmalar Merkezi. Yaşamın Kalitesi, Y\xf6netimin Kalitesi ile Artar."}),(0,a.jsxs)(r(),{href:"https://www.mgsam.com",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center text-primary hover:text-primary/80 font-medium group text-xs md:text-sm",children:["SİTEYİ ZİYARET ET",(0,a.jsx)("svg",{className:"w-3 h-3 md:w-4 md:h-4 ml-1 md:ml-2 transform group-hover:translate-x-1 transition-transform",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-5 lg:p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col min-h-[280px] md:min-h-[320px] lg:min-h-[360px]",children:[(0,a.jsx)("div",{className:"w-20 h-20 md:w-24 md:h-24 lg:w-32 lg:h-32 mb-3 md:mb-4 mx-auto flex items-center justify-center",children:(0,a.jsx)(n.default,{src:"/logos/logo2.webp",alt:"Meta Analiz Haber",width:160,height:160,className:"object-contain w-full h-full",loading:"lazy",sizes:"(max-width: 768px) 80px, (max-width: 1024px) 96px, 128px"})}),(0,a.jsxs)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold mb-2 md:mb-3 text-gray-900 text-center leading-tight",children:["Meta Analiz",(0,a.jsx)("br",{}),"Haber"]}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-5 flex-grow text-center text-xs md:text-sm leading-relaxed",children:"\xd6zg\xfcr ve Bağımsız Haber Platformu. Ger\xe7ek Haberciliğin Adresi."}),(0,a.jsxs)(r(),{href:"https://www.metaanalizhaber.com",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center text-primary hover:text-primary/80 font-medium group text-xs md:text-sm",children:["SİTEYİ ZİYARET ET",(0,a.jsx)("svg",{className:"w-3 h-3 md:w-4 md:h-4 ml-1 md:ml-2 transform group-hover:translate-x-1 transition-transform",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-5 lg:p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col min-h-[280px] md:min-h-[320px] lg:min-h-[360px]",children:[(0,a.jsx)("div",{className:"w-20 h-20 md:w-24 md:h-24 lg:w-32 lg:h-32 mb-3 md:mb-4 mx-auto flex items-center justify-center",children:(0,a.jsx)(n.default,{src:"/logos/logo3.webp",alt:"Meta Analiz M\xfcşavirlik",width:160,height:160,className:"object-contain w-full h-full",loading:"lazy",sizes:"(max-width: 768px) 80px, (max-width: 1024px) 96px, 128px"})}),(0,a.jsx)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold mb-2 md:mb-3 text-gray-900 text-center leading-tight",children:"Meta Analiz M\xfcşavirlik"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-5 flex-grow text-center text-xs md:text-sm leading-relaxed",children:"Hayal Değil, D\xfcşlerinizi Ger\xe7eğe D\xf6n\xfcşt\xfcr\xfcn. Profesyonel \xc7\xf6z\xfcmler."}),(0,a.jsxs)(r(),{href:"https://www.metaanalizmusavirlik.com",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center text-primary hover:text-primary/80 font-medium group text-xs md:text-sm",children:["SİTEYİ ZİYARET ET",(0,a.jsx)("svg",{className:"w-3 h-3 md:w-4 md:h-4 ml-1 md:ml-2 transform group-hover:translate-x-1 transition-transform",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-5 lg:p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col min-h-[280px] md:min-h-[320px] lg:min-h-[360px]",children:[(0,a.jsx)("div",{className:"w-20 h-20 md:w-24 md:h-24 lg:w-32 lg:h-32 mb-3 md:mb-4 mx-auto flex items-center justify-center",children:(0,a.jsx)(n.default,{src:"/logos/logo4.webp",alt:"Meta Analiz \xd6zel Eğitim",width:160,height:160,className:"object-contain w-full h-full",loading:"lazy",sizes:"(max-width: 768px) 80px, (max-width: 1024px) 96px, 128px"})}),(0,a.jsxs)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold mb-2 md:mb-3 text-gray-900 text-center leading-tight",children:["Meta Analiz",(0,a.jsx)("br",{}),"\xd6zel Eğitim"]}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-5 flex-grow text-center text-xs md:text-sm leading-relaxed",children:"Hedefe Giden Adımlarda Başarınız Başarımızdır. Kaliteli Eğitim Hizmetleri."}),(0,a.jsxs)(r(),{href:"https://www.metaanalizegitim.com",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center text-primary hover:text-primary/80 font-medium group text-xs md:text-sm",children:["SİTEYİ ZİYARET ET",(0,a.jsx)("svg",{className:"w-3 h-3 md:w-4 md:h-4 ml-1 md:ml-2 transform group-hover:translate-x-1 transition-transform",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-5 lg:p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex flex-col min-h-[280px] md:min-h-[320px] lg:min-h-[360px] col-span-2 md:col-span-1",children:[(0,a.jsx)("div",{className:"w-20 h-20 md:w-24 md:h-24 lg:w-32 lg:h-32 mb-3 md:mb-4 mx-auto flex items-center justify-center",children:(0,a.jsx)(n.default,{src:"/logos/logo5.webp",alt:"Meta Analiz E-Ticaret",width:160,height:160,className:"object-contain w-full h-full",loading:"lazy",sizes:"(max-width: 768px) 80px, (max-width: 1024px) 96px, 128px"})}),(0,a.jsxs)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold mb-2 md:mb-3 text-gray-900 text-center leading-tight",children:["Meta Analiz",(0,a.jsx)("br",{}),"E-Ticaret"]}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-5 flex-grow text-center text-xs md:text-sm leading-relaxed",children:"\xd6zg\xfcr ve G\xfcvenli Alışverişin Adresi. Yenilik\xe7i E-Ticaret \xc7\xf6z\xfcmleri."}),(0,a.jsxs)("div",{className:"flex flex-col gap-2 md:gap-3",children:[(0,a.jsxs)("button",{disabled:!0,className:"inline-flex items-center justify-center text-gray-700 cursor-not-allowed font-medium text-xs md:text-sm",children:["TEMASSIZ AL ",(0,a.jsx)("span",{className:"ml-1 md:ml-2 text-sm text-gray-700",children:"(Yakında)"})]}),(0,a.jsxs)("button",{disabled:!0,className:"inline-flex items-center justify-center text-gray-700 cursor-not-allowed font-medium text-xs md:text-sm",children:["\xd6ZG\xdcR AL ",(0,a.jsx)("span",{className:"ml-1 md:ml-2 text-sm text-gray-700",children:"(Yakında)"})]})]})]})]})]})}),(0,a.jsxs)("section",{className:"py-16 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-700 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=%2240%22 height=%2240%22 viewBox=%220 0 40 40%22 xmlns=%22http://www.w3.org/2000/svg%22%3E%3Cg fill=%22none%22 fill-rule=%22evenodd%22%3E%3Cg fill=%22%23ffffff%22 fill-opacity=%220.03%22%3E%3Cpath d=%22m0 40l40-40h-40z%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"}),(0,a.jsx)("div",{className:"absolute top-0 right-0 w-1/3 h-full bg-gradient-to-l from-slate-600/20 to-transparent"}),(0,a.jsx)("div",{className:"container mx-auto px-4 relative z-10",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsxs)("div",{className:"inline-flex items-center bg-slate-700/30 backdrop-blur-sm rounded-full px-4 py-2 mb-4 border border-slate-600/30",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-gradient-to-r from-green-400 to-green-300 rounded-full mr-2"}),(0,a.jsx)("span",{className:"text-white font-medium",children:"Sizi Arayalım"})]}),(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-extralight text-white mb-3 tracking-tight",children:"Geri Arama Talep Edin"}),(0,a.jsx)("p",{className:"text-lg text-slate-300 max-w-2xl mx-auto font-light",children:"Bilgilerinizi bırakın, uzmanlarımız en kısa s\xfcrede sizinle iletişime ge\xe7sin"})]}),(0,a.jsxs)("form",{id:"callbackForm",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[(0,a.jsx)("div",{children:(0,a.jsx)("input",{type:"text",id:"name",name:"name",required:!0,className:"w-full px-4 py-3 bg-white border border-gray-300 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent shadow-sm",placeholder:"Adınız Soyadınız"})}),(0,a.jsx)("div",{children:(0,a.jsx)("input",{type:"tel",id:"phone",name:"phone",required:!0,className:"w-full px-4 py-3 bg-white border border-gray-300 rounded-2xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent shadow-sm",placeholder:"Telefon Numaranız"})}),(0,a.jsx)("div",{children:(0,a.jsxs)("select",{id:"preferredTime",name:"preferredTime",defaultValue:"",className:"w-full px-4 py-3 bg-white border border-gray-300 rounded-2xl text-gray-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent shadow-sm",children:[(0,a.jsx)("option",{value:"",disabled:!0,className:"text-gray-500",children:"Tercih ettiğiniz zaman dilimi"}),(0,a.jsx)("option",{value:"morning",className:"text-gray-900",children:"Sabah (09:00-12:00)"}),(0,a.jsx)("option",{value:"afternoon",className:"text-gray-900",children:"\xd6ğleden Sonra (13:00-17:00)"}),(0,a.jsx)("option",{value:"evening",className:"text-gray-900",children:"Akşam (18:00-20:00)"})]})})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("button",{type:"submit",className:"glass-btn inline-flex items-center px-8 py-3 text-lg font-medium text-white hover:scale-105 transition-all duration-300",children:[(0,a.jsx)("span",{children:"Sizi Arayalım"}),(0,a.jsx)("svg",{className:"w-5 h-5 ml-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14 5l7 7m0 0l-7 7m7-7H3"})})]})})]})]})})]}),(0,a.jsx)("section",{className:"py-24 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"max-w-3xl mx-auto text-center mb-16",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-slate-100 rounded-full text-slate-700 text-sm font-medium mb-6",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-gradient-to-r from-green-500 to-green-400 rounded-full mr-3"})," ","Hizmet Alanlarımız"]}),(0,a.jsxs)("h2",{className:"text-4xl md:text-5xl font-light mb-4",children:["Profesyonel ",(0,a.jsxs)("span",{className:"text-primary",children:[" ","\xc7\xf6z\xfcmlerimiz"]})]}),(0,a.jsx)("p",{className:"text-gray-700 text-lg leading-relaxed",children:"İşletmenizi bir adım \xf6teye taşıyacak stratejik hizmetlerimizle tanışın"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8 max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,a.jsx)("div",{className:"w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6",children:(0,a.jsx)("svg",{className:"w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})}),(0,a.jsx)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4",children:"Stratejik Danışmanlık"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base",children:"Kurumunuzun geleceğini şekillendiren stratejik kararlar almasına yardımcı oluyoruz."})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,a.jsx)("div",{className:"w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6",children:(0,a.jsx)("svg",{className:"w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,a.jsx)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4",children:"S\xfcre\xe7 İyileştirme"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base",children:"İş s\xfcre\xe7lerinizi optimize ederek verimliliğinizi arttırıyoruz."})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,a.jsx)("div",{className:"w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6",children:(0,a.jsx)("svg",{className:"w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 0 1 9-9"})})}),(0,a.jsx)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4",children:"Dijital D\xf6n\xfcş\xfcm"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base",children:"Dijital \xe7ağa uyum sağlamanız i\xe7in teknolojik \xe7\xf6z\xfcmler sunuyoruz."})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,a.jsx)("div",{className:"w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6",children:(0,a.jsx)("svg",{className:"w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})}),(0,a.jsx)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4",children:"Eğitim ve Gelişim"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base",children:"Personel gelişimi ve kurumsal eğitim programları ile insan kaynağınızı g\xfc\xe7lendiriyoruz."})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,a.jsx)("div",{className:"w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6",children:(0,a.jsx)("svg",{className:"w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),(0,a.jsx)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4",children:"Mali Danışmanlık | M\xfcşavirlik"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base",children:"Muhasebe, vergi danışmanlığı ve mali analiz hizmetleri ile finansal yapınızı g\xfc\xe7lendiriyoruz."})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl p-4 md:p-6 lg:p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1",children:[(0,a.jsx)("div",{className:"w-12 h-12 md:w-14 md:h-14 lg:w-16 lg:h-16 bg-primary/10 rounded-xl flex items-center justify-center text-primary mb-4 md:mb-6",children:(0,a.jsxs)("svg",{className:"w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:[(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,a.jsx)("h3",{className:"text-sm md:text-lg lg:text-xl font-semibold text-gray-900 mb-2 md:mb-4",children:"Gayrimenkul Danışmanlığı"}),(0,a.jsx)("p",{className:"text-gray-700 mb-4 md:mb-6 text-xs md:text-sm lg:text-base",children:"Gayrimenkul yatırımları ve emlak değerlendirme konularında uzman danışmanlık hizmeti sunuyoruz."})]})]})]})}),(0,a.jsx)(m.A,{}),(0,a.jsx)("script",{dangerouslySetInnerHTML:{__html:`
          document.addEventListener('DOMContentLoaded', function() {
            const callbackForm = document.getElementById('callbackForm');
            if (callbackForm) {
              callbackForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                const submitBtn = this.querySelector('button[type="submit"]');
                const originalHTML = submitBtn.innerHTML;

                // Show loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = 'G\xf6nderiliyor...';

                try {
                  const formData = new FormData(this);
                  const fullName = formData.get('name').trim();
                  const nameParts = fullName.split(' ');
                  const name = nameParts[0] || '';
                  const surname = nameParts.slice(1).join(' ') || '';

                  const data = {
                    name: name,
                    surname: surname,
                    phone: formData.get('phone'),
                    preferredTime: formData.get('preferredTime'),
                    message: formData.get('message')
                  };

                  const response = await fetch('/api/callback.php', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                  });

                  const result = await response.json();

                  if (result.success) {
                    // Success message
                    submitBtn.innerHTML = '✓ G\xf6nderildi';
                    submitBtn.className = submitBtn.className.replace('glass-btn', 'glass-btn-success');

                    // Reset form
                    this.reset();

                    // Show success alert
                    alert('Geri arama talebiniz başarıyla alındı! En kısa s\xfcrede sizinle iletişime ge\xe7eceğiz.');

                    // Reset button after 3 seconds
                    setTimeout(() => {
                      submitBtn.disabled = false;
                      submitBtn.innerHTML = originalHTML;
                      submitBtn.className = submitBtn.className.replace('glass-btn-success', 'glass-btn');
                    }, 3000);
                  } else {
                    throw new Error(result.message || 'Bir hata oluştu');
                  }
                } catch (error) {
                  console.error('Callback request error:', error);
                  alert('Hata: ' + error.message);

                  // Reset button
                  submitBtn.disabled = false;
                  submitBtn.innerHTML = originalHTML;
                }
              });
            }
          });
        `}})]})}},1227:(e,t,s)=>{Promise.resolve().then(s.bind(s,554))}},e=>{var t=t=>e(e.s=t);e.O(0,[766,874,49,441,684,358],()=>t(1227)),_N_E=e.O()}]);