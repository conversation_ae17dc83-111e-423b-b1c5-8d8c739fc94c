(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345],{1024:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>a});var n=l(5155),r=l(6874),u=l.n(r);function a(){return(0,n.jsx)("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-slate-50 to-white",children:(0,n.jsxs)("div",{className:"text-center p-8 bg-white rounded-lg shadow-lg border border-slate-200",children:[(0,n.jsx)("h1",{className:"text-6xl font-light text-slate-900 mb-4",children:"404"}),(0,n.jsx)("h2",{className:"text-2xl font-light text-slate-700 mb-4",children:"<PERSON><PERSON> Bulunamadı"}),(0,n.jsx)("p",{className:"text-slate-600 mb-8 font-light",children:"Aradığınız sayfa mevcut değil veya taşınmış olabilir."}),(0,n.jsx)(u(),{href:"/",className:"inline-block px-8 py-3 bg-slate-900 text-white rounded-xl hover:bg-slate-800 transition-colors duration-300 font-medium",children:"Ana Sayfaya D\xf6n"})]})})}},6616:(e,t,l)=>{Promise.resolve().then(l.bind(l,1024))},6654:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return r}});let n=l(2115);function r(e,t){let l=(0,n.useRef)(null),r=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=l.current;e&&(l.current=null,e());let t=r.current;t&&(r.current=null,t())}else e&&(l.current=u(e,n)),t&&(r.current=u(t,n))},[e,t])}function u(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let l=e(t);return"function"==typeof l?l:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}},e=>{var t=t=>e(e.s=t);e.O(0,[874,441,684,358],()=>t(6616)),_N_E=e.O()}]);